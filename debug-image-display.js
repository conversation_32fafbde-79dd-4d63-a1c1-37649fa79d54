/**
 * 调试图片显示问题
 */

const CryptoJS = require('crypto-js');

// 解密函数
function decrypt(cipherText, key = "TwksVZsnurnMwo1tWUWm47URI0vTAtzn4g3aHFddFeM=", iv = "tkGB6z24JlJcr5HxvPMYLQ==") {
  try {
    const keyWordArray = CryptoJS.enc.Base64.parse(key);
    const ivWordArray = CryptoJS.enc.Base64.parse(iv);
    const decrypted = CryptoJS.AES.decrypt(cipherText, keyWordArray, {
      iv: ivWordArray,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7
    });
    return decrypted.toString(CryptoJS.enc.Utf8);
  } catch (error) {
    console.error('解密失败:', error);
    throw error;
  }
}

console.log('=== 调试图片显示问题 ===\n');

async function debugImageDisplay() {
  try {
    console.log('1. 调用视频API...');
    const response = await fetch('https://api.player.4ii.cc/api/v1/videos', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify({
        maxResultCount: 3,
        skipCount: 0,
        sorting: 'VideoAddTime desc'
      })
    });

    const responseData = await response.json();
    console.log('✅ API调用成功');

    if (responseData.success && responseData.data) {
      console.log('\n2. 解密数据...');
      const decryptedText = decrypt(responseData.data);
      const decryptedData = JSON.parse(decryptedText);
      console.log('✅ 数据解密成功');

      if (decryptedData.Items && decryptedData.Items.length > 0) {
        console.log('\n3. 分析视频数据...');
        const videos = decryptedData.Items.slice(0, 3);
        
        videos.forEach((video, index) => {
          console.log(`\n--- 视频 ${index + 1} ---`);
          console.log('ID:', video.Id);
          console.log('标题:', video.VideoName);
          console.log('原始图片字段 VideoPic:', video.VideoPic);
          
          // 检查图片URL格式
          if (video.VideoPic) {
            console.log('图片URL类型:', typeof video.VideoPic);
            console.log('图片URL长度:', video.VideoPic.length);
            console.log('是否以http开头:', video.VideoPic.startsWith('http'));
            console.log('是否包含api路径:', video.VideoPic.includes('/api/'));
            
            // 如果是相对路径，构造完整URL
            let fullImageUrl = video.VideoPic;
            if (!video.VideoPic.startsWith('http')) {
              fullImageUrl = 'https://api.player.4ii.cc' + video.VideoPic;
              console.log('构造的完整URL:', fullImageUrl);
            }
            
            // 测试图片URL是否可访问
            console.log('测试图片URL可访问性...');
            testImageUrl(fullImageUrl, index + 1);
          } else {
            console.log('❌ 没有图片URL');
          }
        });
        
      } else {
        console.log('❌ 没有视频数据');
      }
    } else {
      console.log('❌ API返回失败');
    }
    
  } catch (error) {
    console.error('调试失败:', error.message);
  }
}

async function testImageUrl(imageUrl, videoIndex) {
  try {
    console.log(`  测试视频${videoIndex}的图片URL: ${imageUrl}`);
    
    const response = await fetch(imageUrl, { 
      method: 'HEAD',  // 只获取头部信息，不下载整个文件
      cache: 'no-cache'
    });
    
    console.log(`  状态码: ${response.status}`);
    console.log(`  Content-Type: ${response.headers.get('content-type')}`);
    console.log(`  Content-Length: ${response.headers.get('content-length')}`);
    
    if (response.ok) {
      console.log(`  ✅ 视频${videoIndex}图片URL可访问`);
      
      // 测试实际下载图片数据
      const fullResponse = await fetch(imageUrl, { cache: 'default' });
      const buffer = await fullResponse.arrayBuffer();
      console.log(`  原始图片大小: ${buffer.byteLength} bytes`);
      
      // 检查前4个字节
      const firstFourBytes = new Uint8Array(buffer.slice(0, 4));
      const hexBytes = Array.from(firstFourBytes).map(b => b.toString(16).padStart(2, '0')).join(' ');
      console.log(`  前4个字节: ${hexBytes}`);
      
      // 检查是否为正常图片格式
      const isJPEG = firstFourBytes[0] === 0xFF && firstFourBytes[1] === 0xD8;
      const isPNG = firstFourBytes[0] === 0x89 && firstFourBytes[1] === 0x50;
      console.log(`  是否为JPEG: ${isJPEG}`);
      console.log(`  是否为PNG: ${isPNG}`);
      
      if (!isJPEG && !isPNG) {
        console.log(`  ⚠️ 需要移除前4个字节`);
        const processedBuffer = buffer.slice(4);
        console.log(`  处理后大小: ${processedBuffer.byteLength} bytes`);
        
        // 检查处理后的前几个字节
        const processedFirstBytes = new Uint8Array(processedBuffer.slice(0, 4));
        const processedHex = Array.from(processedFirstBytes).map(b => b.toString(16).padStart(2, '0')).join(' ');
        console.log(`  处理后前4个字节: ${processedHex}`);
        
        const processedIsJPEG = processedFirstBytes[0] === 0xFF && processedFirstBytes[1] === 0xD8;
        const processedIsPNG = processedFirstBytes[0] === 0x89 && processedFirstBytes[1] === 0x50;
        console.log(`  处理后是否为JPEG: ${processedIsJPEG}`);
        console.log(`  处理后是否为PNG: ${processedIsPNG}`);
      }
      
    } else {
      console.log(`  ❌ 视频${videoIndex}图片URL不可访问`);
    }
    
  } catch (error) {
    console.log(`  ❌ 视频${videoIndex}图片测试失败: ${error.message}`);
  }
  
  console.log(''); // 空行分隔
}

// 运行调试
debugImageDisplay().then(() => {
  console.log('\n=== 调试完成 ===');
  console.log('\n可能的问题：');
  console.log('1. 图片URL格式问题');
  console.log('2. 图片需要移除前4个字节');
  console.log('3. 网络访问权限问题');
  console.log('4. 组件渲染问题');
});
