<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API调用测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .result {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            border-left: 4px solid #28a745;
        }
        .error {
            border-left: 4px solid #dc3545;
            color: #dc3545;
        }
        .loading {
            border-left: 4px solid #007bff;
            color: #007bff;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>API调用测试</h1>
        
        <div class="test-section">
            <div class="test-title">1. 直接API调用测试</div>
            <button onclick="testDirectAPI()">测试导航API</button>
            <div id="direct-result" class="result loading">点击按钮开始测试...</div>
        </div>
        
        <div class="test-section">
            <div class="test-title">2. 解密功能测试</div>
            <button onclick="testDecryption()">测试解密功能</button>
            <div id="decrypt-result" class="result loading">点击按钮开始测试...</div>
        </div>
        
        <div class="test-section">
            <div class="test-title">3. 完整流程测试</div>
            <button onclick="testFullFlow()">测试完整流程</button>
            <div id="full-result" class="result loading">点击按钮开始测试...</div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/crypto-js@4.2.0/crypto-js.min.js"></script>
    <script>
        // 解密函数
        function decrypt(cipherText, key = "TwksVZsnurnMwo1tWUWm47URI0vTAtzn4g3aHFddFeM=", iv = "tkGB6z24JlJcr5HxvPMYLQ==") {
            try {
                console.log('开始解密...');
                const keyWordArray = CryptoJS.enc.Base64.parse(key);
                const ivWordArray = CryptoJS.enc.Base64.parse(iv);
                const decrypted = CryptoJS.AES.decrypt(cipherText, keyWordArray, {
                    iv: ivWordArray,
                    mode: CryptoJS.mode.CBC,
                    padding: CryptoJS.pad.Pkcs7
                });
                const result = decrypted.toString(CryptoJS.enc.Utf8);
                console.log('解密完成，结果长度:', result.length);
                return result;
            } catch (error) {
                console.error('解密失败:', error);
                throw error;
            }
        }

        // 测试直接API调用
        async function testDirectAPI() {
            const resultDiv = document.getElementById('direct-result');
            resultDiv.className = 'result loading';
            resultDiv.textContent = '正在调用API...';
            
            try {
                const response = await fetch('https://api.player.4ii.cc/api/v1/navigations/pc', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                resultDiv.className = 'result success';
                resultDiv.textContent = `API调用成功！\n\n响应状态: ${response.status}\n响应数据: ${JSON.stringify(data, null, 2)}`;
                
                return data;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `API调用失败: ${error.message}`;
                throw error;
            }
        }

        // 测试解密功能
        function testDecryption() {
            const resultDiv = document.getElementById('decrypt-result');
            resultDiv.className = 'result loading';
            resultDiv.textContent = '正在测试解密...';
            
            try {
                // 使用您提供的真实数据
                const testData = "FcIVR+ti6AnEriiFqXmE/n5j0W622KaODa9Po9CpwyCEEPDEtPQDzk/xTPFQ5iBZQMATLq0t2TABfmfQ41z7lrjM+V37C0EQMJLOJbpmcA5EqLIANDA3aUcKhz9371oiGSSXGsc7of7k8P47oGc+OoRDwu/qazy2sTwfQHOqeTiiGTtiqIriQdLfr0qhas4401gYGOFN+mi35sU+SZ420t3K4uLEGl5ZO/LYruB9EOsSkB64G3TP9F8Hl4CAMlOtY++IPlzirR0vBHN7ta4x7u2zj125FYl1OrMl7DuMsWv8nMC6YBtRpKNlXaRDHtURr+KYNZSarb+LR5FgdN8GoXOQE8/le2olwVdVAY+E3IQlG8D4iijLcndl5T+HjpDk";
                
                const decryptedString = decrypt(testData);
                const parsedData = JSON.parse(decryptedString);
                
                resultDiv.className = 'result success';
                resultDiv.textContent = `解密成功！\n\n解密后的字符串长度: ${decryptedString.length}\n解析后的数据: ${JSON.stringify(parsedData, null, 2)}`;
                
                return parsedData;
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `解密失败: ${error.message}`;
                throw error;
            }
        }

        // 测试完整流程
        async function testFullFlow() {
            const resultDiv = document.getElementById('full-result');
            resultDiv.className = 'result loading';
            resultDiv.textContent = '正在测试完整流程...';
            
            try {
                // 1. 调用API
                const apiResponse = await fetch('https://api.player.4ii.cc/api/v1/navigations/pc', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    }
                });
                
                const responseData = await apiResponse.json();
                
                let result = `步骤1: API调用成功\n`;
                result += `响应状态: ${apiResponse.status}\n`;
                result += `响应代码: ${responseData.code}\n`;
                result += `响应消息: ${responseData.message}\n\n`;
                
                // 2. 检查是否需要解密
                if (responseData.code === 200 && responseData.data) {
                    result += `步骤2: 检测到加密数据，开始解密\n`;
                    result += `密文长度: ${responseData.data.length}\n\n`;
                    
                    // 3. 解密数据
                    const decryptedString = decrypt(responseData.data);
                    result += `步骤3: 解密成功\n`;
                    result += `解密后长度: ${decryptedString.length}\n\n`;
                    
                    // 4. 解析JSON
                    const finalData = JSON.parse(decryptedString);
                    result += `步骤4: JSON解析成功\n`;
                    result += `数据类型: ${Array.isArray(finalData) ? 'Array' : typeof finalData}\n`;
                    
                    if (Array.isArray(finalData)) {
                        result += `数组长度: ${finalData.length}\n`;
                        if (finalData.length > 0) {
                            result += `第一项数据: ${JSON.stringify(finalData[0], null, 2)}\n`;
                        }
                    }
                    
                    result += `\n完整数据: ${JSON.stringify(finalData, null, 2)}`;
                    
                    resultDiv.className = 'result success';
                    resultDiv.textContent = result;
                } else {
                    result += `步骤2: 无需解密，直接返回数据\n`;
                    result += `最终数据: ${JSON.stringify(responseData, null, 2)}`;
                    
                    resultDiv.className = 'result success';
                    resultDiv.textContent = result;
                }
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `完整流程测试失败: ${error.message}\n\n错误详情: ${error.stack}`;
            }
        }
    </script>
</body>
</html>
