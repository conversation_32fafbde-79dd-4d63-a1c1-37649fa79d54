/**
 * 测试导航数据解密和字段名修复
 */

const CryptoJS = require('crypto-js');

// 解密函数
function decrypt(cipherText, key = "TwksVZsnurnMwo1tWUWm47URI0vTAtzn4g3aHFddFeM=", iv = "tkGB6z24JlJcr5HxvPMYLQ==") {
  try {
    const keyWordArray = CryptoJS.enc.Base64.parse(key);
    const ivWordArray = CryptoJS.enc.Base64.parse(iv);
    const decrypted = CryptoJS.AES.decrypt(cipherText, keyWordArray, {
      iv: ivWordArray,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7
    });
    return decrypted.toString(CryptoJS.enc.Utf8);
  } catch (error) {
    console.error('解密失败:', error);
    throw error;
  }
}

// 过滤移动端显示的导航项（修复后的版本）
function filterMobileNavigations(navigations) {
  if (!navigations || !Array.isArray(navigations)) {
    return [];
  }
  return navigations.filter(nav => nav.IsH5Display || nav.isH5Display);
}

// 转换为标签数据（修复后的版本）
function convertToTabs(navigations) {
  if (!navigations || !Array.isArray(navigations)) {
    return [];
  }

  // 确保始终有推荐标签
  const tabs = [
    { id: 'recommend', name: '推荐' }
  ];

  // 添加其他导航项
  navigations.forEach(nav => {
    // 兼容大小写字段名
    const isH5Display = nav.IsH5Display || nav.isH5Display;
    const name = nav.Name || nav.name;
    const routerLink = nav.RouterLink || nav.routerLink;
    const categotyId = nav.CategotyId || nav.categotyId;
    const categotyName = nav.CategotyName || nav.categotyName;
    const isExternalLink = nav.IsExternalLink || nav.isExternalLink;
    const extraParams = nav.ExtraParams || nav.extraParams;
    
    if (isH5Display && name) {
      // 将导航名称转换为小写并移除空格，作为ID
      const id = routerLink ?
        routerLink.replace(/^\//, '').replace(/\//g, '-') :
        name.toLowerCase().replace(/\s+/g, '');

      tabs.push({
        id: id,
        name: name,
        categotyId: categotyId,
        categotyName: categotyName,
        routerLink: routerLink,
        isExternalLink: isExternalLink,
        extraParams: extraParams
      });
    }
  });

  return tabs;
}

console.log('=== 导航数据解密和字段名修复测试 ===\n');

try {
  // 使用您提供的真实数据
  const testData = "FcIVR+ti6AnEriiFqXmE/n5j0W622KaODa9Po9CpwyCEEPDEtPQDzk/xTPFQ5iBZQMATLq0t2TABfmfQ41z7lrjM+V37C0EQMJLOJbpmcA5EqLIANDA3aUcKhz9371oiGSSXGsc7of7k8P47oGc+OoRDwu/qazy2sTwfQHOqeTiiGTtiqIriQdLfr0qhas4401gYGOFN+mi35sU+SZ420t3K4uLEGl5ZO/LYruB9EOsSkB64G3TP9F8Hl4CAMlOtY++IPlzirR0vBHN7ta4x7u2zj125FYl1OrMl7DuMsWv8nMC6YBtRpKNlXaRDHtURr+KYNZSarb+LR5FgdN8GoXOQE8/le2olwVdVAY+E3IQlG8D4iijLcndl5T+HjpDk";
  
  console.log('1. 解密导航数据...');
  const decryptedString = decrypt(testData);
  const navigationData = JSON.parse(decryptedString);
  
  console.log('解密成功！');
  console.log('原始数据:', JSON.stringify(navigationData, null, 2));
  console.log('');
  
  console.log('2. 测试字段名兼容性...');
  
  // 测试第一个导航项的字段
  if (navigationData.length > 0) {
    const firstNav = navigationData[0];
    console.log('第一个导航项的字段:');
    console.log('- Name:', firstNav.Name);
    console.log('- IsH5Display:', firstNav.IsH5Display);
    console.log('- CategotyId:', firstNav.CategotyId);
    console.log('- CategotyName:', firstNav.CategotyName);
    console.log('');
  }
  
  console.log('3. 测试过滤移动端导航项...');
  const mobileNavigations = filterMobileNavigations(navigationData);
  console.log('过滤后的移动端导航项数量:', mobileNavigations.length);
  console.log('过滤后的数据:', JSON.stringify(mobileNavigations, null, 2));
  console.log('');
  
  console.log('4. 测试转换为标签数据...');
  const tabs = convertToTabs(mobileNavigations);
  console.log('转换后的标签数量:', tabs.length);
  console.log('标签数据:', JSON.stringify(tabs, null, 2));
  console.log('');
  
  console.log('✅ 所有测试通过！导航数据解密和字段名修复功能正常工作。');
  
} catch (error) {
  console.error('❌ 测试失败:', error.message);
  console.error('错误详情:', error);
}

console.log('\n=== 测试完成 ===');
