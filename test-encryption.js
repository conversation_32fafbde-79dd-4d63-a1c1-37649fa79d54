/**
 * 测试加密解密功能
 * 这个文件用于验证request.js中的加密解密功能是否正常工作
 */

// 模拟uni环境
global.uni = {
  request: () => {},
  getStorageSync: () => null,
  removeStorageSync: () => {},
  showToast: () => {}
};

// 模拟logger
const logger = {
  log: console.log,
  error: console.error,
  warn: console.warn
};

// 模拟config
const config = {
  apiBaseUrl: 'https://api.player.4ii.cc',
  timeout: 10000,
  appVersion: '1.0.0'
};

// 导入crypto-js
const CryptoJS = require('crypto-js');

// 复制request.js中的decrypt方法进行测试
function decrypt(cipherText, key = "TwksVZsnurnMwo1tWUWm47URI0vTAtzn4g3aHFddFeM=", iv = "tkGB6z24JlJcr5HxvPMYLQ==") {
  try {
    // 使用 crypto-js 替代 Node.js 的 crypto
    const keyWordArray = CryptoJS.enc.Base64.parse(key);
    const ivWordArray = CryptoJS.enc.Base64.parse(iv);
    const decrypted = CryptoJS.AES.decrypt(cipherText, keyWordArray, {
      iv: ivWordArray,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7
    });
    return decrypted.toString(CryptoJS.enc.Utf8);
  } catch (error) {
    console.error('解密失败:', error);
    throw new Error('数据解密失败');
  }
}

// 测试加密功能（用于生成测试数据）
function encrypt(plainText, key = "TwksVZsnurnMwo1tWUWm47URI0vTAtzn4g3aHFddFeM=", iv = "tkGB6z24JlJcr5HxvPMYLQ==") {
  try {
    const keyWordArray = CryptoJS.enc.Base64.parse(key);
    const ivWordArray = CryptoJS.enc.Base64.parse(iv);
    const encrypted = CryptoJS.AES.encrypt(plainText, keyWordArray, {
      iv: ivWordArray,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7
    });
    return encrypted.toString();
  } catch (error) {
    console.error('加密失败:', error);
    throw new Error('数据加密失败');
  }
}

// 测试数据
const testData = {
  code: 200,
  message: "success",
  data: {
    videos: [
      {
        id: "1",
        title: "测试视频1",
        videoType: "电影",
        videoScore: 8.5
      },
      {
        id: "2", 
        title: "测试视频2",
        videoType: "电视剧",
        videoScore: 9.0
      }
    ],
    totalCount: 2
  }
};

console.log('=== 加密解密功能测试 ===\n');

try {
  // 1. 测试加密
  console.log('1. 测试加密功能:');
  const jsonString = JSON.stringify(testData);
  console.log('原始数据:', jsonString);
  
  const encryptedData = encrypt(jsonString);
  console.log('加密后数据:', encryptedData);
  console.log('加密成功 ✓\n');

  // 2. 测试解密
  console.log('2. 测试解密功能:');
  const decryptedData = decrypt(encryptedData);
  console.log('解密后数据:', decryptedData);
  
  // 3. 验证数据完整性
  console.log('3. 验证数据完整性:');
  const parsedData = JSON.parse(decryptedData);
  console.log('解析后的数据:', JSON.stringify(parsedData, null, 2));
  
  // 比较原始数据和解密后的数据
  const isEqual = JSON.stringify(testData) === JSON.stringify(parsedData);
  console.log('数据完整性验证:', isEqual ? '通过 ✓' : '失败 ✗');
  
  if (isEqual) {
    console.log('\n🎉 所有测试通过！加密解密功能正常工作。');
  } else {
    console.log('\n❌ 测试失败！数据在加密解密过程中发生了变化。');
  }

} catch (error) {
  console.error('\n❌ 测试过程中发生错误:', error.message);
  console.error('错误详情:', error);
}

console.log('\n=== 测试完成 ===');
