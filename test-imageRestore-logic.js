/**
 * 测试 ImageRestore 组件逻辑
 */

console.log('=== 测试 ImageRestore 组件逻辑 ===\n');

// 模拟组件的 fullImageUrl 计算逻辑
function computeFullImageUrl(imageUrl) {
  if (!imageUrl) return '';

  // 如果已经是完整URL，直接返回
  if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
    return imageUrl;
  }

  // 如果是相对路径，拼接当前域名
  const baseUrl = 'https://api.player.4ii.cc'; // 模拟移动端的baseUrl
  return baseUrl + imageUrl;
}

// 测试不同类型的图片URL
const testUrls = [
  // 完整URL
  'https://placehold.co/300x400',
  'https://placehold.co/500x283',
  
  // 相对路径（模拟API返回的格式）
  '/api/v1/image/video/3a1a4839f21a0e147758b6b2562a73d6',
  '/api/v1/image/video/3a1a48396ff4c8168c660037b406d1ee',
  
  // 空值测试
  '',
  null,
  undefined
];

console.log('1. 测试 fullImageUrl 计算逻辑：\n');

testUrls.forEach((url, index) => {
  console.log(`测试 ${index + 1}:`);
  console.log(`  输入: ${url}`);
  console.log(`  输出: ${computeFullImageUrl(url)}`);
  console.log('');
});

// 模拟组件的图片处理逻辑
async function testImageProcessing(imageUrl) {
  console.log(`\n测试图片处理: ${imageUrl}`);
  
  if (!imageUrl) {
    console.log('  ❌ 图片URL为空');
    return false;
  }
  
  try {
    // 模拟 fetch 请求
    const response = await fetch(imageUrl, { cache: 'default' });
    console.log(`  状态码: ${response.status}`);
    
    if (!response.ok) {
      console.log(`  ❌ 请求失败: ${response.status} ${response.statusText}`);
      return false;
    }

    // 获取图片数据
    const buffer = await response.arrayBuffer();
    console.log(`  原始大小: ${buffer.byteLength} bytes`);

    // 移除前4个字节
    const newBuffer = buffer.slice(4);
    console.log(`  处理后大小: ${newBuffer.byteLength} bytes`);

    // 创建blob
    const blob = new Blob([newBuffer], { type: response.headers.get('content-type') || 'image/jpeg' });
    const objectURL = URL.createObjectURL(blob);
    console.log(`  ✅ 生成blob URL: ${objectURL.substring(0, 50)}...`);
    
    // 清理
    URL.revokeObjectURL(objectURL);
    return true;
    
  } catch (error) {
    console.log(`  ❌ 处理失败: ${error.message}`);
    return false;
  }
}

// 测试图片处理
async function runImageProcessingTests() {
  console.log('\n2. 测试图片处理逻辑：');
  
  const testImageUrls = [
    'https://placehold.co/300x400',
    'https://placehold.co/500x283'
  ];
  
  for (const url of testImageUrls) {
    await testImageProcessing(url);
  }
}

// 模拟组件在不同环境下的行为
function simulateComponentBehavior() {
  console.log('\n3. 模拟组件行为：');
  
  const mockVideoItem = {
    id: 1,
    title: '测试视频',
    videoPic: '/api/v1/image/video/test123',
    poster: null,
    videoName: '测试视频名称'
  };
  
  console.log('模拟视频数据:', mockVideoItem);
  
  // 模拟组件中的图片URL逻辑
  const imageUrl = mockVideoItem.videoPic || mockVideoItem.poster || 'https://placehold.co/300x400';
  console.log('组件使用的imageUrl:', imageUrl);
  
  const fullImageUrl = computeFullImageUrl(imageUrl);
  console.log('计算的fullImageUrl:', fullImageUrl);
  
  // 模拟非H5环境的逻辑
  console.log('\n非H5环境下的处理:');
  console.log('  processedImageUrl初始值:', imageUrl);
  console.log('  image标签src:', `processedImageUrl || fullImageUrl = ${imageUrl || fullImageUrl}`);
  
  // 模拟H5环境的逻辑
  console.log('\nH5环境下的处理:');
  console.log('  img标签src:', fullImageUrl);
  console.log('  会调用fetchImageAndRemoveFirstFourBytes进行处理');
}

// 运行所有测试
async function runAllTests() {
  simulateComponentBehavior();
  await runImageProcessingTests();
  
  console.log('\n=== 测试完成 ===');
  console.log('\n分析结果:');
  console.log('1. fullImageUrl计算逻辑正常');
  console.log('2. 图片处理逻辑正常（对于可访问的URL）');
  console.log('3. 组件逻辑结构正确');
  console.log('\n可能的问题:');
  console.log('1. API返回的图片URL可能无法访问');
  console.log('2. 移动端环境可能有网络限制');
  console.log('3. 需要检查实际的API响应数据');
}

runAllTests().catch(console.error);
