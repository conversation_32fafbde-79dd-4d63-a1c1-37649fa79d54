/**
 * 测试视频数据结构和图片URL
 */

const CryptoJS = require('crypto-js');

// 解密函数
function decrypt(cipherText, key = "TwksVZsnurnMwo1tWUWm47URI0vTAtzn4g3aHFddFeM=", iv = "tkGB6z24JlJcr5HxvPMYLQ==") {
  try {
    const keyWordArray = CryptoJS.enc.Base64.parse(key);
    const ivWordArray = CryptoJS.enc.Base64.parse(iv);
    const decrypted = CryptoJS.AES.decrypt(cipherText, keyWordArray, {
      iv: ivWordArray,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7
    });
    return decrypted.toString(CryptoJS.enc.Utf8);
  } catch (error) {
    console.error('解密失败:', error);
    throw error;
  }
}

// 模拟API调用来获取视频数据
async function testVideoAPI() {
  console.log('=== 测试视频API数据结构 ===\n');

  try {
    // 调用视频API
    const response = await fetch('https://api.player.4ii.cc/api/v1/videos', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify({
        maxResultCount: 5,
        skipCount: 0,
        sorting: 'VideoAddTime desc'
      })
    });

    const responseData = await response.json();
    console.log('1. API响应状态:', response.status);
    console.log('2. 响应代码:', responseData.code);
    console.log('3. 响应消息:', responseData.message);

    if (responseData.code === 200 && responseData.data) {
      console.log('4. 开始解密视频数据...');
      const decryptedString = decrypt(responseData.data);
      const videoData = JSON.parse(decryptedString);

      console.log('5. 解密成功！');
      console.log('6. 视频数据类型:', Array.isArray(videoData) ? 'Array' : typeof videoData);
      console.log('7. 视频数据内容预览:', JSON.stringify(videoData).substring(0, 500) + '...');

      if (Array.isArray(videoData)) {
        console.log('7. 视频数量:', videoData.length);

        if (videoData.length > 0) {
          console.log('\n8. 第一个视频的完整数据结构:');
          console.log(JSON.stringify(videoData[0], null, 2));

          console.log('\n9. 图片相关字段分析:');
          const firstVideo = videoData[0];
          console.log('- videoPic:', firstVideo.videoPic || 'undefined');
          console.log('- poster:', firstVideo.poster || 'undefined');
          console.log('- videoName:', firstVideo.videoName || 'undefined');
          console.log('- id:', firstVideo.id || 'undefined');

          // 检查图片URL是否可访问
          if (firstVideo.videoPic) {
            console.log('\n10. 测试图片URL可访问性:');
            console.log('图片URL:', firstVideo.videoPic);

            try {
              const imgResponse = await fetch(firstVideo.videoPic, { method: 'HEAD' });
              console.log('图片URL状态:', imgResponse.status);
              console.log('图片Content-Type:', imgResponse.headers.get('content-type'));
            } catch (imgError) {
              console.log('图片URL访问失败:', imgError.message);
            }
          }
        }
      } else if (videoData && videoData.items) {
        console.log('7. 视频数据在items字段中，数量:', videoData.items.length);

        if (videoData.items.length > 0) {
          console.log('\n8. 第一个视频的完整数据结构:');
          console.log(JSON.stringify(videoData.items[0], null, 2));

          console.log('\n9. 图片相关字段分析:');
          const firstVideo = videoData.items[0];
          console.log('- videoPic:', firstVideo.videoPic || 'undefined');
          console.log('- poster:', firstVideo.poster || 'undefined');
          console.log('- videoName:', firstVideo.videoName || 'undefined');
          console.log('- id:', firstVideo.id || 'undefined');
        }
      }
    } else {
      console.log('4. API返回错误或无数据');
    }

  } catch (error) {
    console.error('测试失败:', error.message);
  }
}

// 运行测试
testVideoAPI().then(() => {
  console.log('\n=== 测试完成 ===');
});
