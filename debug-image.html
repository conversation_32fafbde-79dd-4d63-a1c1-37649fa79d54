<!DOCTYPE html>
<html>
<head>
    <title>图片调试测试</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-section { margin: 20px 0; border: 1px solid #ccc; padding: 15px; }
        .test-image { width: 200px; height: 150px; border: 1px solid #ddd; margin: 10px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
    </style>
</head>
<body>
    <h1>图片显示调试测试</h1>
    
    <div class="test-section">
        <h3>1. 测试占位图片</h3>
        <img class="test-image" src="https://placehold.co/300x400" alt="占位图1" onload="console.log('占位图1加载成功')" onerror="console.log('占位图1加载失败')">
        <img class="test-image" src="https://placehold.co/500x283" alt="占位图2" onload="console.log('占位图2加载成功')" onerror="console.log('占位图2加载失败')">
    </div>

    <div class="test-section">
        <h3>2. 测试图片处理逻辑</h3>
        <button onclick="testImageProcessing()">测试图片处理</button>
        <div id="processResult"></div>
    </div>

    <div class="test-section">
        <h3>3. 控制台日志</h3>
        <p class="info">请打开浏览器控制台查看详细日志</p>
    </div>

    <script>
        console.log('=== 图片调试测试开始 ===');
        
        // 测试图片处理逻辑
        async function testImageProcessing() {
            const testUrl = 'https://placehold.co/300x400';
            const resultDiv = document.getElementById('processResult');
            
            try {
                console.log('开始测试图片处理:', testUrl);
                resultDiv.innerHTML = '<p class="info">正在处理图片...</p>';
                
                const response = await fetch(testUrl, { cache: 'default' });
                console.log('Fetch响应状态:', response.status, response.statusText);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const buffer = await response.arrayBuffer();
                console.log('原始图片大小:', buffer.byteLength, 'bytes');
                
                const newBuffer = buffer.slice(4);
                console.log('处理后图片大小:', newBuffer.byteLength, 'bytes');
                
                const blob = new Blob([newBuffer], { type: response.headers.get('content-type') || 'image/jpeg' });
                const objectURL = URL.createObjectURL(blob);
                
                console.log('生成的blob URL:', objectURL);
                
                // 创建测试图片
                const testImg = document.createElement('img');
                testImg.className = 'test-image';
                testImg.src = objectURL;
                testImg.onload = () => {
                    console.log('✅ 处理后的图片加载成功');
                    resultDiv.innerHTML = '<p class="success">✅ 图片处理成功！</p>';
                    URL.revokeObjectURL(objectURL);
                };
                testImg.onerror = () => {
                    console.log('❌ 处理后的图片加载失败');
                    resultDiv.innerHTML = '<p class="error">❌ 处理后的图片加载失败</p>';
                };
                
                resultDiv.appendChild(testImg);
                
            } catch (error) {
                console.error('图片处理失败:', error);
                resultDiv.innerHTML = `<p class="error">❌ 处理失败: ${error.message}</p>`;
            }
        }
        
        // 测试基本图片加载
        function testBasicImageLoad() {
            const testUrls = [
                'https://placehold.co/300x400',
                'https://placehold.co/500x283',
                'https://via.placeholder.com/300x400'
            ];
            
            testUrls.forEach((url, index) => {
                const img = new Image();
                img.onload = () => console.log(`✅ 基本图片${index + 1}加载成功:`, url);
                img.onerror = () => console.log(`❌ 基本图片${index + 1}加载失败:`, url);
                img.src = url;
            });
        }
        
        // 页面加载完成后执行测试
        window.onload = () => {
            console.log('页面加载完成，开始测试...');
            testBasicImageLoad();
        };
    </script>
</body>
</html>
