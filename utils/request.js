/**
 * 请求工具类
 * 封装uni.request，添加拦截器、错误处理和重试机制
 */
import { config } from './config';
import logger from './logger';
import CryptoJS from 'crypto-js';

// 重试配置
const retryConfig = {
  maxRetries: 3,
  retryDelay: 1000,
  retryStatusCodes: [408, 429, 500, 502, 503, 504]
};

/**
 * 请求类
 */
class Request {
  constructor() {
    this.baseUrl = config.apiBaseUrl;
    this.timeout = config.timeout;
    this.header = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'X-App-Version': config.appVersion,
      'X-Platform': 'uniapp'
    };
  }

  /**
   * 获取认证令牌
   */
  async getAuthToken() {
    try {
      return uni.getStorageSync('AUTH_TOKEN');
    } catch (e) {
      console.warn('获取认证令牌失败:', e);
      return null;
    }
  }

  /**
   * 处理未授权情况
   */
  async handleUnauthorized() {
    try {
      uni.removeStorageSync('AUTH_TOKEN');
      // 跳转到登录页面
      // uni.navigateTo({ url: '/pages/login/login' });
    } catch (e) {
      console.error('处理未授权失败:', e);
    }
  }

  /**
   * 判断是否需要重试
   */
  shouldRetry(statusCode, retryCount) {
    return (
      retryCount < retryConfig.maxRetries &&
      retryConfig.retryStatusCodes.includes(statusCode)
    );
  }

  /**
   * 解密响应数据
   * @param {string} cipherText - 加密的数据
   * @param {string} key - 解密密钥
   * @param {string} iv - 初始化向量
   * @returns {string} 解密后的数据
   */
  decrypt(cipherText, key = "TwksVZsnurnMwo1tWUWm47URI0vTAtzn4g3aHFddFeM=", iv = "tkGB6z24JlJcr5HxvPMYLQ==") {
    try {
      logger.info('[Decrypt Method] 开始解密过程');
      logger.info('[Decrypt Method] 密文类型:', typeof cipherText);
      logger.info('[Decrypt Method] 密文长度:', cipherText ? cipherText.length : 'null');

      if (!cipherText) {
        throw new Error('密文为空');
      }

      // 使用 crypto-js 替代 Node.js 的 crypto
      const keyWordArray = CryptoJS.enc.Base64.parse(key);
      const ivWordArray = CryptoJS.enc.Base64.parse(iv);

      logger.log('[Decrypt Method] 密钥和IV解析成功');

      const decrypted = CryptoJS.AES.decrypt(cipherText, keyWordArray, {
        iv: ivWordArray,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
      });

      logger.log('[Decrypt Method] AES解密完成');

      const result = decrypted.toString(CryptoJS.enc.Utf8);

      logger.log('[Decrypt Method] UTF8转换完成，结果长度:', result ? result.length : 'null');

      if (!result) {
        throw new Error('解密结果为空');
      }

      return result;
    } catch (error) {
      logger.error('解密失败:', error);
      logger.error('解密参数:', {
        cipherTextLength: cipherText ? cipherText.length : 'null',
        keyLength: key ? key.length : 'null',
        ivLength: iv ? iv.length : 'null'
      });
      throw new Error('数据解密失败: ' + error.message);
    }
  }

  /**
   * 执行请求
   */
  async request(options) {
    const { url, method, data, header, retryCount = 0 } = options;

    // 合并请求头
    const mergedHeader = { ...this.header, ...header };

    // 添加认证信息
    const token = await this.getAuthToken();
    if (token) {
      mergedHeader['Authorization'] = `Bearer ${token}`;
    }

    // 输出请求日志
    logger.log(`[Request] ${method || 'GET'} ${this.baseUrl + url}`, data);

    return new Promise((resolve, reject) => {
      uni.request({
        url: this.baseUrl + url,
        method: method || 'GET',
        data: data,
        header: mergedHeader,
        timeout: this.timeout,
        success: (res) => {
          // 输出响应日志
          logger.log(`[Response] ${method || 'GET'} ${url}`, res.statusCode, res.data);

          // 处理响应
          if (res.statusCode === 200) {
            // 检查业务状态码
            if (res.data.code === 200) {
              try {
                logger.info(`[Decrypt] 开始解密响应数据: ${url}`);
                logger.info(`[Decrypt] 密文长度: ${res.data.data ? res.data.data.length : 'null'}`);

                // 解密响应数据
                const strJson = this.decrypt(res.data.data);
                logger.info(`[Decrypt] 解密成功，结果长度: ${strJson ? strJson.length : 'null'}`);
                logger.info(`[Decrypt] 解密结果预览: ${strJson ? strJson.substring(0, 200) + '...' : 'null'}`);

                const decryptedData = JSON.parse(strJson);
                logger.info(`[Decrypt] JSON解析成功，数据类型: ${Array.isArray(decryptedData) ? 'Array' : typeof decryptedData}`);

                if (Array.isArray(decryptedData)) {
                  logger.info(`[Decrypt] 数组长度: ${decryptedData.length}`);
                }

                // 为了与现有代码兼容，直接返回解密后的数据
                resolve(decryptedData);
              } catch (decryptError) {
                logger.error('解密或解析数据失败:', decryptError);
                logger.error('原始响应数据:', res.data);
                const error = new Error('数据解密失败');
                error.code = -3;
                error.url = url;
                error.originalError = decryptError;
                reject(error);
              }
            } else {
              const error = new Error(res.data.message || '请求失败');
              error.code = res.data.code;
              error.url = url;
              error.response = res.data;
              console.error(`[Error] ${method || 'GET'} ${url}`, error);
              reject(error);
            }
          } else if (res.statusCode === 401) {
            // 未授权
            this.handleUnauthorized();
            reject(new Error('未授权，请重新登录'));
          } else if (this.shouldRetry(res.statusCode, retryCount)) {
            // 需要重试
            logger.log(`[Retry] ${method || 'GET'} ${url} - 尝试第${retryCount + 1}次`);
            setTimeout(() => {
              this.request({
                ...options,
                retryCount: retryCount + 1
              }).then(resolve).catch(reject);
            }, retryConfig.retryDelay * Math.pow(2, retryCount));
          } else {
            // 其他HTTP错误
            const error = new Error(`HTTP错误: ${res.statusCode}`);
            error.code = res.statusCode;
            error.url = url;
            error.response = res.data;
            logger.error(`[Error] ${method || 'GET'} ${url}`, error);
            reject(error);
          }
        },
        fail: (err) => {
          // 输出错误日志
          logger.error(`[Fail] ${method || 'GET'} ${url}`, err);

          // 网络错误或超时
          if (err.errMsg.includes('timeout')) {
            const error = new Error('请求超时，请稍后重试');
            error.code = -2;
            error.url = url;
            reject(error);
          } else if (this.shouldRetry(0, retryCount)) {
            // 网络错误重试
            logger.log(`[Retry] ${method || 'GET'} ${url} - 尝试第${retryCount + 1}次`);
            setTimeout(() => {
              this.request({
                ...options,
                retryCount: retryCount + 1
              }).then(resolve).catch(reject);
            }, retryConfig.retryDelay * Math.pow(2, retryCount));
          } else {
            const error = new Error('网络连接失败，请检查网络设置');
            error.code = -1;
            error.url = url;
            error.originalError = err;
            reject(error);
          }
        }
      });
    });
  }

  /**
   * GET请求
   */
  get(url, params, header) {
    return this.request({
      url: params ? `${url}?${this.paramsToString(params)}` : url,
      method: 'GET',
      header
    });
  }

  /**
   * POST请求
   */
  post(url, data, header) {
    return this.request({
      url,
      method: 'POST',
      data,
      header
    });
  }

  /**
   * 将参数对象转换为URL查询字符串
   */
  paramsToString(params) {
    return Object.keys(params)
      .filter(key => params[key] !== undefined && params[key] !== null)
      .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
      .join('&');
  }
}

/**
 * 统一错误处理
 * @param {Error|string} error - 错误对象或错误消息
 * @param {boolean} [showToast=true] - 是否显示Toast提示
 * @param {string} [context=''] - 错误上下文
 */
export function handleError(error, showToast = true, context = '') {
  const errorMessage = error instanceof Error ? error.message : error;
  const fullMessage = context ? `[${context}] ${errorMessage}` : errorMessage;

  // 记录到控制台
  logger.error(fullMessage, error);

  // 显示Toast提示
  if (showToast) {
    uni.showToast({
      title: errorMessage.length > 20 ? errorMessage.substring(0, 20) + '...' : errorMessage,
      icon: 'none',
      duration: 2000
    });
  }
}

// 导出单例
export default new Request();
