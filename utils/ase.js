const crypto = require('crypto');

function encrypt(plainText, key, iv) {
    const cipher = crypto.createCipheriv('aes-256-cbc', Buffer.from(key, 'base64'), Buffer.from(iv, 'base64'));
    let encrypted = cipher.update(plainText, 'utf8', 'base64');
    encrypted += cipher.final('base64');
    return encrypted;
}

function decrypt(cipherText, key, iv) {
    const decipher = crypto.createDecipheriv('aes-256-cbc', Buffer.from(key, 'base64'), Buffer.from(iv, 'base64'));
    let decrypted = decipher.update(cipherText, 'base64', 'utf8');
    decrypted += decipher.final('utf8');
    return decrypted;
}

function generateKeyAndIV() {
    const key = crypto.randomBytes(32); // AES-256 key
    const iv = crypto.randomBytes(16);  // IV for CBC
    return {
        key: key.toString('base64'),
        iv: iv.toString('base64')
    };
}

// Example usage (Node.js):
// const { key, iv } = generateKeyAndIV();
// const encrypted = encrypt("Hello, World!", key, iv);
// console.log("Encrypted:", encrypted);
// console.log("Decrypted:", decrypt(encrypted, key, iv));