/**
 * 请求工具类
 * 封装uni.request，添加拦截器、错误处理和重试机制
 */
import { config } from './config';
import logger from './logger';

// 重试配置
const retryConfig = {
  maxRetries: 3,
  retryDelay: 1000,
  retryStatusCodes: [408, 429, 500, 502, 503, 504]
};

/**
 * 请求类
 */
class Request {
  constructor() {
    this.baseUrl = config.apiBaseUrl;
    this.timeout = config.timeout;
    this.header = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'X-App-Version': config.appVersion,
      'X-Platform': 'uniapp'
    };
  }

  /**
   * 获取认证令牌
   */
  async getAuthToken() {
    try {
      return uni.getStorageSync('AUTH_TOKEN');
    } catch (e) {
      console.warn('获取认证令牌失败:', e);
      return null;
    }
  }

  /**
   * 处理未授权情况
   */
  async handleUnauthorized() {
    try {
      uni.removeStorageSync('AUTH_TOKEN');
      // 跳转到登录页面
      // uni.navigateTo({ url: '/pages/login/login' });
    } catch (e) {
      console.error('处理未授权失败:', e);
    }
  }

  /**
   * 判断是否需要重试
   */
  shouldRetry(statusCode, retryCount) {
    return (
      retryCount < retryConfig.maxRetries &&
      retryConfig.retryStatusCodes.includes(statusCode)
    );
  }
  decrypt(cipherText, ke="TwksVZsnurnMwo1tWUWm47URI0vTAtzn4g3aHFddFeM=", iv="tkGB6z24JlJcr5HxvPMYLQ==") {

    const decipher = crypto.createDecipheriv('aes-256-cbc', Buffer.from(key, 'base64'), Buffer.from(iv, 'base64'));
    let decrypted = decipher.update(cipherText, 'base64', 'utf8');
    decrypted += decipher.final('utf8');
    return decrypted;
  }
  /**
   * 执行请求
   */
  async request(options) {
    const { url, method, data, header, retryCount = 0 } = options;

    // 合并请求头
    const mergedHeader = { ...this.header, ...header };

    // 添加认证信息
    const token = await this.getAuthToken();
    if (token) {
      mergedHeader['Authorization'] = `Bearer ${token}`;
    }

    // 输出请求日志
    logger.log(`[Request] ${method || 'GET'} ${this.baseUrl + url}`, data);

    return new Promise((resolve, reject) => {
      uni.request({
        url: this.baseUrl + url,
        method: method || 'GET',
        data: data,
        header: mergedHeader,
        timeout: this.timeout,
        success: (res) => {
          // 输出响应日志
          logger.log(`[Response] ${method || 'GET'} ${url}`, res.statusCode, res.data);

          // 处理响应
          if (res.statusCode === 200) {
            // 检查业务状态码
            if (res.data.code === 200) {
              const strJson = this.decrypt(res.data.data);
              const data = JSON.parse(strJson);
              resolve(res.data.data);
            } else {
              const error = new Error(res.data.message || '请求失败');
              error.code = res.data.code;
              error.url = url;
              error.response = res.data;
              console.error(`[Error] ${method || 'GET'} ${url}`, error);
              reject(error);
            }
          } else if (res.statusCode === 401) {
            // 未授权
            this.handleUnauthorized();
            reject(new Error('未授权，请重新登录'));
          } else if (this.shouldRetry(res.statusCode, retryCount)) {
            // 需要重试
            logger.log(`[Retry] ${method || 'GET'} ${url} - 尝试第${retryCount + 1}次`);
            setTimeout(() => {
              this.request({
                ...options,
                retryCount: retryCount + 1
              }).then(resolve).catch(reject);
            }, retryConfig.retryDelay * Math.pow(2, retryCount));
          } else {
            // 其他HTTP错误
            const error = new Error(`HTTP错误: ${res.statusCode}`);
            error.code = res.statusCode;
            error.url = url;
            error.response = res.data;
            logger.error(`[Error] ${method || 'GET'} ${url}`, error);
            reject(error);
          }
        },
        fail: (err) => {
          // 输出错误日志
          logger.error(`[Fail] ${method || 'GET'} ${url}`, err);

          // 网络错误或超时
          if (err.errMsg.includes('timeout')) {
            const error = new Error('请求超时，请稍后重试');
            error.code = -2;
            error.url = url;
            reject(error);
          } else if (this.shouldRetry(0, retryCount)) {
            // 网络错误重试
            logger.log(`[Retry] ${method || 'GET'} ${url} - 尝试第${retryCount + 1}次`);
            setTimeout(() => {
              this.request({
                ...options,
                retryCount: retryCount + 1
              }).then(resolve).catch(reject);
            }, retryConfig.retryDelay * Math.pow(2, retryCount));
          } else {
            const error = new Error('网络连接失败，请检查网络设置');
            error.code = -1;
            error.url = url;
            error.originalError = err;
            reject(error);
          }
        }
      });
    });
  }

  /**
   * GET请求
   */
  get(url, params, header) {
    return this.request({
      url: params ? `${url}?${this.paramsToString(params)}` : url,
      method: 'GET',
      header
    });
  }

  /**
   * POST请求
   */
  post(url, data, header) {
    return this.request({
      url,
      method: 'POST',
      data,
      header
    });
  }

  /**
   * 将参数对象转换为URL查询字符串
   */
  paramsToString(params) {
    return Object.keys(params)
      .filter(key => params[key] !== undefined && params[key] !== null)
      .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
      .join('&');
  }
}

/**
 * 统一错误处理
 * @param {Error|string} error - 错误对象或错误消息
 * @param {boolean} [showToast=true] - 是否显示Toast提示
 * @param {string} [context=''] - 错误上下文
 */
export function handleError(error, showToast = true, context = '') {
  const errorMessage = error instanceof Error ? error.message : error;
  const fullMessage = context ? `[${context}] ${errorMessage}` : errorMessage;

  // 记录到控制台
  logger.error(fullMessage, error);

  // 显示Toast提示
  if (showToast) {
    uni.showToast({
      title: errorMessage.length > 20 ? errorMessage.substring(0, 20) + '...' : errorMessage,
      icon: 'none',
      duration: 2000
    });
  }
}

// 导出单例
export default new Request();
