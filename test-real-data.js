/**
 * 测试真实数据解密
 */

const CryptoJS = require('crypto-js');

// 复制request.js中的decrypt方法
function decrypt(cipherText, key = "TwksVZsnurnMwo1tWUWm47URI0vTAtzn4g3aHFddFeM=", iv = "tkGB6z24JlJcr5HxvPMYLQ==") {
  try {
    console.log('开始解密...');
    console.log('密文长度:', cipherText.length);
    console.log('密钥:', key);
    console.log('IV:', iv);
    
    // 使用 crypto-js 替代 Node.js 的 crypto
    const keyWordArray = CryptoJS.enc.Base64.parse(key);
    const ivWordArray = CryptoJS.enc.Base64.parse(iv);
    
    console.log('密钥解析成功');
    console.log('IV解析成功');
    
    const decrypted = CryptoJS.AES.decrypt(cipherText, keyWordArray, {
      iv: ivWordArray,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7
    });
    
    console.log('AES解密完成');
    
    const result = decrypted.toString(CryptoJS.enc.Utf8);
    console.log('转换为UTF8完成');
    console.log('解密结果长度:', result.length);
    
    return result;
  } catch (error) {
    console.error('解密失败:', error);
    throw new Error('数据解密失败');
  }
}

// 您提供的真实数据
const realData = {
    "data": "FcIVR+ti6AnEriiFqXmE/n5j0W622KaODa9Po9CpwyCEEPDEtPQDzk/xTPFQ5iBZQMATLq0t2TABfmfQ41z7lrjM+V37C0EQMJLOJbpmcA5EqLIANDA3aUcKhz9371oiGSSXGsc7of7k8P47oGc+OoRDwu/qazy2sTwfQHOqeTiiGTtiqIriQdLfr0qhas4401gYGOFN+mi35sU+SZ420t3K4uLEGl5ZO/LYruB9EOsSkB64G3TP9F8Hl4CAMlOtY++IPlzirR0vBHN7ta4x7u2zj125FYl1OrMl7DuMsWv8nMC6YBtRpKNlXaRDHtURr+KYNZSarb+LR5FgdN8GoXOQE8/le2olwVdVAY+E3IQlG8D4iijLcndl5T+HjpDk",
    "code": 200,
    "message": "Success"
};

console.log('=== 真实数据解密测试 ===\n');

try {
  console.log('原始响应数据:', JSON.stringify(realData, null, 2));
  console.log('\n开始解密data字段...');
  
  const decryptedString = decrypt(realData.data);
  console.log('\n解密后的字符串:', decryptedString);
  
  if (decryptedString) {
    console.log('\n尝试解析JSON...');
    const parsedData = JSON.parse(decryptedString);
    console.log('解析后的数据:', JSON.stringify(parsedData, null, 2));
    console.log('\n✅ 解密和解析成功！');
  } else {
    console.log('\n❌ 解密结果为空');
  }
  
} catch (error) {
  console.error('\n❌ 处理过程中发生错误:', error.message);
  console.error('错误详情:', error);
  
  // 尝试其他可能的问题
  console.log('\n=== 问题排查 ===');
  
  // 检查密文是否有效
  try {
    const testDecode = CryptoJS.enc.Base64.parse(realData.data);
    console.log('✅ Base64解码成功，密文格式正确');
  } catch (e) {
    console.log('❌ Base64解码失败，密文格式错误:', e.message);
  }
  
  // 检查密钥和IV
  try {
    const keyTest = CryptoJS.enc.Base64.parse("TwksVZsnurnMwo1tWUWm47URI0vTAtzn4g3aHFddFeM=");
    const ivTest = CryptoJS.enc.Base64.parse("tkGB6z24JlJcr5HxvPMYLQ==");
    console.log('✅ 密钥和IV格式正确');
  } catch (e) {
    console.log('❌ 密钥或IV格式错误:', e.message);
  }
}

console.log('\n=== 测试完成 ===');
