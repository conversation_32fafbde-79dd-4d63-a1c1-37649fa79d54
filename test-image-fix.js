/**
 * 测试图片显示修复
 */

const CryptoJS = require('crypto-js');

// 解密函数
function decrypt(cipherText, key = "TwksVZsnurnMwo1tWUWm47URI0vTAtzn4g3aHFddFeM=", iv = "tkGB6z24JlJcr5HxvPMYLQ==") {
  try {
    const keyWordArray = CryptoJS.enc.Base64.parse(key);
    const ivWordArray = CryptoJS.enc.Base64.parse(iv);
    const decrypted = CryptoJS.AES.decrypt(cipherText, keyWordArray, {
      iv: ivWordArray,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7
    });
    return decrypted.toString(CryptoJS.enc.Utf8);
  } catch (error) {
    console.error('解密失败:', error);
    throw error;
  }
}

// 修复后的数据处理函数
function formatVideoItem(video) {
  if (!video) return {};

  // 兼容大小写字段名
  const id = video.Id || video.id;
  const videoId = video.VideoId || video.videoId;
  const videoName = video.VideoName || video.videoName || video.title || '';
  const videoPic = video.VideoPic || video.videoPic || video.poster || 'https://placehold.co/300x400';
  const videoType = video.VideoType || video.videoType || '';
  const videoTag = video.VideoTag || video.videoTag || '';
  const videoScore = video.VideoScore || video.videoScore || 0;
  const videoHits = video.VideoHits || video.videoHits || 0;

  return {
    id: id,
    videoId: videoId,
    title: videoName,
    poster: videoPic,
    videoName: videoName,
    videoPic: videoPic,
    videoType: videoType,
    videoTag: videoTag,
    videoScore: videoScore,
    videoHits: videoHits
  };
}

console.log('=== 测试图片显示修复 ===\n');

async function testImageFix() {
  try {
    // 调用视频API
    const response = await fetch('https://api.player.4ii.cc/api/v1/videos', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify({
        maxResultCount: 3,
        skipCount: 0,
        sorting: 'VideoAddTime desc'
      })
    });
    
    const responseData = await response.json();
    console.log('1. API调用成功');
    
    if (responseData.code === 200 && responseData.data) {
      console.log('2. 开始解密数据...');
      const decryptedString = decrypt(responseData.data);
      const videoData = JSON.parse(decryptedString);
      
      console.log('3. 解密成功！');
      
      if (videoData && videoData.Items) {
        console.log('4. 找到视频数据，数量:', videoData.Items.length);
        
        // 测试前3个视频的图片字段
        videoData.Items.slice(0, 3).forEach((video, index) => {
          console.log(`\n视频 ${index + 1}:`);
          console.log('- 原始字段名:');
          console.log('  * Id:', video.Id);
          console.log('  * VideoName:', video.VideoName);
          console.log('  * VideoPic:', video.VideoPic);
          console.log('  * VideoScore:', video.VideoScore);
          
          // 使用修复后的函数处理
          const formattedVideo = formatVideoItem(video);
          console.log('- 修复后的数据:');
          console.log('  * id:', formattedVideo.id);
          console.log('  * title:', formattedVideo.title);
          console.log('  * videoPic:', formattedVideo.videoPic);
          console.log('  * poster:', formattedVideo.poster);
          console.log('  * videoScore:', formattedVideo.videoScore);
          
          // 检查图片URL是否有效
          if (formattedVideo.videoPic && formattedVideo.videoPic !== 'https://placehold.co/300x400') {
            console.log('  * 图片URL有效:', formattedVideo.videoPic);
          } else {
            console.log('  * 使用默认图片');
          }
        });
        
        console.log('\n✅ 图片字段修复测试完成！');
        console.log('现在所有视频都应该能正确显示图片了。');
        
      } else {
        console.log('4. 未找到视频数据');
      }
    } else {
      console.log('2. API返回错误或无数据');
    }
    
  } catch (error) {
    console.error('测试失败:', error.message);
  }
}

// 运行测试
testImageFix().then(() => {
  console.log('\n=== 测试完成 ===');
});
