<!DOCTYPE html>
<html>
<head>
    <title>图片显示测试</title>
    <style>
        .test-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            padding: 20px;
        }
        .test-item {
            width: 200px;
            border: 1px solid #ccc;
            padding: 10px;
        }
        .test-image {
            width: 100%;
            height: 150px;
            object-fit: cover;
            border: 1px solid #ddd;
        }
        .test-info {
            margin-top: 10px;
            font-size: 12px;
        }
        .error {
            color: red;
        }
        .success {
            color: green;
        }
    </style>
</head>
<body>
    <h1>图片显示测试</h1>
    <div id="testContainer" class="test-container"></div>

    <script>
        // 测试图片URL
        const testUrls = [
            'https://placehold.co/300x400',
            'https://placehold.co/500x283',
            'https://via.placeholder.com/300x400',
            'https://picsum.photos/300/400',
            // 添加一些可能的真实图片URL进行测试
        ];

        const container = document.getElementById('testContainer');

        testUrls.forEach((url, index) => {
            const testItem = document.createElement('div');
            testItem.className = 'test-item';
            
            const img = document.createElement('img');
            img.className = 'test-image';
            img.src = url;
            
            const info = document.createElement('div');
            info.className = 'test-info';
            info.innerHTML = `<strong>测试 ${index + 1}</strong><br>URL: ${url}`;
            
            img.onload = () => {
                info.innerHTML += '<br><span class="success">✅ 加载成功</span>';
            };
            
            img.onerror = () => {
                info.innerHTML += '<br><span class="error">❌ 加载失败</span>';
            };
            
            testItem.appendChild(img);
            testItem.appendChild(info);
            container.appendChild(testItem);
        });

        // 测试图片处理函数
        async function testImageProcessing(imageUrl) {
            try {
                console.log('测试图片处理:', imageUrl);
                const response = await fetch(imageUrl, { cache: 'default' });
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }
                
                const buffer = await response.arrayBuffer();
                console.log('原始图片大小:', buffer.byteLength);
                
                const newBuffer = buffer.slice(4);
                console.log('处理后图片大小:', newBuffer.byteLength);
                
                const blob = new Blob([newBuffer], { type: response.headers.get('content-type') || 'image/jpeg' });
                const objectURL = URL.createObjectURL(blob);
                
                console.log('生成的blob URL:', objectURL);
                
                // 创建测试图片
                const testImg = document.createElement('img');
                testImg.src = objectURL;
                testImg.style.width = '100px';
                testImg.style.height = '100px';
                testImg.style.border = '2px solid blue';
                testImg.onload = () => console.log('处理后的图片加载成功');
                testImg.onerror = () => console.log('处理后的图片加载失败');
                document.body.appendChild(testImg);
                
                return objectURL;
            } catch (error) {
                console.error('图片处理失败:', error);
                return null;
            }
        }

        // 测试第一个图片的处理
        setTimeout(() => {
            testImageProcessing(testUrls[0]);
        }, 2000);
    </script>
</body>
</html>
