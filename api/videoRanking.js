/**
 * 排行榜相关API
 */
import request from '../utils/request';

/**
 * 排行榜类型枚举
 */
export const RankingType = {
  Day: 0,   // 日榜
  Week: 1,  // 周榜
  Month: 2, // 月榜
  Total: 3  // 总榜
};

/**
 * 显示类型枚举
 */
export const TypeDisplayType = {
  Default: '0',      // 默认
  HomeCategory: '1', // 首页显示-栏目
  HomeRanking: '2',  // 首页显示-栏目-排行
  Ranking: '3',      // 排行榜
};

/**
 * 获取排行榜列表
 * @param {Object} params - 排行榜查询参数
 * @param {Array} params.rankingTypes - 排行榜类型数组
 * @param {String} params.videoType - 视频类型
 * @returns {Promise<Array>} 排行榜数据
 */
export function getVideoRankings(params) {
  return request.post('/api/v1/rankings', {
    rankingTypes: params.rankingTypes || [RankingType.Week],
    videoType: params.videoType || null
  });
}

/**
 * 获取指定类型的排行榜
 * @param {Object} params - 排行榜查询参数
 * @param {String} params.videoType - 视频类型
 * @param {Number} params.rankingType - 排行榜类型
 * @returns {Promise<Array>} 排行榜数据
 */
export function getVideoRankingByType(params) {
  return request.post('/api/v1/rankings/get', {
    videoType: params.videoType,
    rankingType: params.rankingType || RankingType.Week
  });
}

/**
 * 获取视频类型列表
 * @param {Array} [videoTypeIds=[]] - 视频类型ID数组
 * @param {Array} displayTypes - 显示类型数组
 * @param {number} [maxResultCount=200] - 最大返回数量
 * @param {number} [skipCount=0] - 跳过数量
 * @returns {Promise<Array>} 视频类型列表
 */
export function getVideoTypeList(videoTypeIds = [], displayTypes, maxResultCount = 200, skipCount = 0) {
  return request.post('/api/v1/video-types', {
    videoTypeIds: videoTypeIds,
    displayTypes: displayTypes,
    maxResultCount: maxResultCount,
    skipCount: skipCount
  });
}



/**
 * 解析排行榜数据
 * 将API返回的数据转换为页面需要的格式
 * @param {Array|Object} data - 排行榜数据
 * @returns {Array} 格式化后的排行榜数据
 */
export function parseRankingData(data) {
  // 处理不同的数据结构
  let rankings = [];

  // 如果是对象且包含 videoRankings 属性
  if (data && typeof data === 'object' && data.videoRankings) {
    rankings = data.videoRankings;
  }
  // 如果是数组
  else if (Array.isArray(data)) {
    rankings = data;
  }
  // 如果是对象且包含 items 属性
  else if (data && typeof data === 'object' && data.items) {
    rankings = data.items;
  }
  // 如果没有数据
  else {
    return [];
  }

  if (!rankings.length) return [];

  return rankings.map(item => ({
    title: item.VideoName || item.videoName,
    year: (item.VideoAddTime || item.videoAddTime) ? new Date(item.VideoAddTime || item.videoAddTime).getFullYear() + '年' : '',
    tags: (item.VideoTag || item.videoTag) ? (item.VideoTag || item.videoTag).split(',') : [],
    poster: (item.VideoPic || item.videoPic) || 'https://placehold.co/300x400',
    videoId: item.VideoId || item.videoId,
    rank: item.rank,
    score: item.VideoScore || item.videoScore,
    hits: item.VideoHits || item.videoHits,
    actor: item.VideoActor || item.videoActor
  }));
}


