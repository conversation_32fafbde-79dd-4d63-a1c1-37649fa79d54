/**
 * 导航相关API
 */
import request from '../utils/request';

// 导航列表缓存
let navigationCache = null;
// 缓存过期时间（毫秒）
const CACHE_EXPIRATION = 5 * 60 * 1000; // 5分钟
// 缓存时间戳
let cacheTimestamp = 0;

/**
 * 获取导航列表
 * @param {boolean} [forceRefresh=false] - 是否强制刷新
 * @returns {Promise<Array>} 导航列表
 */
export function getNavigationList(forceRefresh = false) {
  const now = Date.now();
  const isCacheValid = navigationCache && (now - cacheTimestamp < CACHE_EXPIRATION);

  // 如果有有效缓存且不强制刷新，直接返回
  if (isCacheValid && !forceRefresh) {
    return Promise.resolve(navigationCache);
  }
  return request.post('/api/v1/navigations/pc').then(response => {
    // 缓存响应数据和时间戳
    navigationCache = response;
    cacheTimestamp = now;
    return response;
  });
}

/**
 * 清除导航列表缓存
 */
export function clearNavigationCache() {
  navigationCache = null;
  cacheTimestamp = 0;
}

/**
 * 过滤移动端显示的导航项
 * @param {Array} navigations - 导航列表
 * @returns {Array} 过滤后的导航列表
 */
export function filterMobileNavigations(navigations) {
  if (!navigations || !Array.isArray(navigations)) {
    return [];
  }

  return navigations.filter(nav => nav.IsH5Display || nav.isH5Display);
}

/**
 * 将导航数据转换为标签数据
 * @param {Array} navigations - 导航列表
 * @returns {Array} 标签数据
 */
export function convertToTabs(navigations) {
  if (!navigations || !Array.isArray(navigations)) {
    return [];
  }

  // 确保始终有推荐标签
  const tabs = [
    { id: 'recommend', name: '推荐' }
  ];

  // 添加其他导航项
  navigations.forEach(nav => {
    // 兼容大小写字段名
    const isH5Display = nav.IsH5Display || nav.isH5Display;
    const name = nav.Name || nav.name;
    const routerLink = nav.RouterLink || nav.routerLink;
    const categotyId = nav.CategotyId || nav.categotyId;
    const categotyName = nav.CategotyName || nav.categotyName;
    const isExternalLink = nav.IsExternalLink || nav.isExternalLink;
    const extraParams = nav.ExtraParams || nav.extraParams;

    if (isH5Display && name) {
      // 将导航名称转换为小写并移除空格，作为ID
      const id = routerLink ?
        routerLink.replace(/^\//, '').replace(/\//g, '-') :
        name.toLowerCase().replace(/\s+/g, '');

      tabs.push({
        id: id,
        name: name,
        categotyId: categotyId,
        categotyName: categotyName,
        routerLink: routerLink,
        isExternalLink: isExternalLink,
        extraParams: extraParams // 添加extraParams字段
      });
    }
  });

  return tabs;
}


