/**
 * 测试视频时长字段的实际数据
 */

const CryptoJS = require('crypto-js');

// 正确的解密函数（从现有代码中复制）
function decrypt(cipherText, key = "TwksVZsnurnMwo1tWUWm47URI0vTAtzn4g3aHFddFeM=", iv = "tkGB6z24JlJcr5HxvPMYLQ==") {
  try {
    const keyWordArray = CryptoJS.enc.Base64.parse(key);
    const ivWordArray = CryptoJS.enc.Base64.parse(iv);
    const decrypted = CryptoJS.AES.decrypt(cipherText, keyWordArray, {
      iv: ivWordArray,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7
    });
    return decrypted.toString(CryptoJS.enc.Utf8);
  } catch (error) {
    console.error('解密失败:', error);
    throw error;
  }
}

console.log('=== 测试视频时长字段数据 ===\n');

async function testVideoDurationData() {
  try {
    // 调用视频API
    const response = await fetch('https://api.player.4ii.cc/api/v1/videos', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify({
        maxResultCount: 5,
        skipCount: 0,
        sorting: 'VideoAddTime desc'
      })
    });

    const responseData = await response.json();
    console.log('1. API调用成功');

    if (responseData.code === 200 && responseData.data) {
      console.log('2. 开始解密数据...');
      const decryptedString = decrypt(responseData.data);
      const videoData = JSON.parse(decryptedString);

      console.log('3. 解密成功！');

      // 检查不同的数据结构
      let items = [];
      if (videoData && videoData.Items) {
        items = videoData.Items;
        console.log('4. 使用Items字段，数量:', items.length);
      } else if (videoData && videoData.items) {
        items = videoData.items;
        console.log('4. 使用items字段，数量:', items.length);
      } else if (Array.isArray(videoData)) {
        items = videoData;
        console.log('4. 直接使用数组，数量:', items.length);
      }

      if (items.length > 0) {
        console.log('\n=== 分析前3个视频的时长相关字段 ===');

        items.slice(0, 3).forEach((video, index) => {
          console.log(`\n--- 视频 ${index + 1} ---`);
          console.log('videoName:', video.videoName || video.VideoName);
          console.log('videoDuration:', video.videoDuration || video.VideoDuration || 'undefined');
          console.log('videoRemarks:', video.videoRemarks || video.VideoRemarks || 'undefined');
          console.log('remarks:', video.remarks || 'undefined');

          // 检查所有可能包含时长信息的字段
          const allFields = Object.keys(video);
          const timeRelatedFields = allFields.filter(field =>
            field.toLowerCase().includes('duration') ||
            field.toLowerCase().includes('time') ||
            field.toLowerCase().includes('remark')
          );

          if (timeRelatedFields.length > 0) {
            console.log('时长相关字段:');
            timeRelatedFields.forEach(field => {
              console.log(`  ${field}:`, video[field]);
            });
          }
        });

        // 检查第一个视频的完整字段列表
        console.log('\n=== 第一个视频的所有字段 ===');
        const firstVideo = items[0];
        Object.keys(firstVideo).forEach(key => {
          console.log(`${key}:`, firstVideo[key]);
        });
      }
    } else {
      console.log('2. API返回错误或无数据');
    }

  } catch (error) {
    console.error('测试失败:', error.message);
  }
}

// 运行测试
testVideoDurationData().then(() => {
  console.log('\n=== 测试完成 ===');
});
