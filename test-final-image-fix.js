/**
 * 测试最终图片显示修复
 */

const CryptoJS = require('crypto-js');

// 解密函数
function decrypt(cipherText, key = "TwksVZsnurnMwo1tWUWm47URI0vTAtzn4g3aHFddFeM=", iv = "tkGB6z24JlJcr5HxvPMYLQ==") {
  try {
    const keyWordArray = CryptoJS.enc.Base64.parse(key);
    const ivWordArray = CryptoJS.enc.Base64.parse(iv);
    const decrypted = CryptoJS.AES.decrypt(cipherText, keyWordArray, {
      iv: ivWordArray,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7
    });
    return decrypted.toString(CryptoJS.enc.Utf8);
  } catch (error) {
    console.error('解密失败:', error);
    throw error;
  }
}

// 模拟页面数据处理（如list.vue中的处理）
function processVideoData(items) {
  return items.map(item => ({
    // 原始字段（为了兼容组件）
    id: item.Id || item.id,
    videoId: item.VideoId || item.videoId,
    videoName: item.VideoName || item.videoName,
    videoPic: (item.VideoPic || item.videoPic) || 'https://placehold.co/300x400',
    videoScore: item.VideoScore || item.videoScore,
    videoHits: item.VideoHits || item.videoHits,
    videoTag: item.VideoTag || item.videoTag,
    videoYear: item.VideoYear || item.videoYear,
    videoActor: item.VideoActor || item.videoActor,
    videoArea: item.VideoArea || item.videoArea,
    videoLang: item.VideoLang || item.videoLang,
    videoDirector: item.VideoDirector || item.videoDirector,
    videoBlurb: item.VideoBlurb || item.videoBlurb,
    videoRemarks: item.VideoRemarks || item.videoRemarks,
    videoDuration: item.VideoDuration || item.videoDuration,

    // 兼容字段（为了兼容原有代码）
    title: item.VideoName || item.videoName,
    year: (item.VideoYear || item.videoYear) || ((item.VideoAddTime || item.videoAddTime) ? new Date(item.VideoAddTime || item.videoAddTime).getFullYear() + '年' : ''),
    tags: (item.VideoTag || item.videoTag) ? (item.VideoTag || item.videoTag).split(',') : [],
    poster: (item.VideoPic || item.videoPic) || 'https://placehold.co/300x400',
    score: item.VideoScore || item.videoScore,
    hits: item.VideoHits || item.videoHits,
    actor: item.VideoActor || item.videoActor,
    area: item.VideoArea || item.videoArea,
    lang: item.VideoLang || item.videoLang,
    director: item.VideoDirector || item.videoDirector,
    blurb: item.VideoBlurb || item.videoBlurb,
    remarks: item.VideoRemarks || item.videoRemarks
  }));
}

// 模拟组件中的图片显示逻辑
function testImageDisplay(processedData) {
  console.log('\n=== 测试组件图片显示逻辑 ===');

  processedData.forEach((item, index) => {
    console.log(`\n视频 ${index + 1}:`);
    console.log('- 标题:', item.title || item.videoName);

    // 模拟HorizontalVideoList组件的图片逻辑
    const horizontalImage = item.poster || item.videoPic || 'https://placehold.co/500x283';
    console.log('- 横屏组件图片URL:', horizontalImage);

    // 模拟VerticalVideoList组件的图片逻辑
    const verticalImage = item.poster || item.videoPic || 'https://placehold.co/300x400';
    console.log('- 竖屏组件图片URL:', verticalImage);

    // 模拟MovieContent组件的图片逻辑
    const movieImage = item.poster || 'https://placehold.co/300x400';
    console.log('- 电影组件图片URL:', movieImage);

    // 检查图片URL是否有效
    const isValidImage = horizontalImage && !horizontalImage.includes('placehold.co');
    console.log('- 图片URL有效:', isValidImage ? '✅' : '❌');

    if (isValidImage) {
      console.log('- 实际图片URL:', horizontalImage);
    }
  });
}

console.log('=== 测试最终图片显示修复 ===\n');

async function testFinalImageFix() {
  try {
    // 调用视频API
    const response = await fetch('https://api.player.4ii.cc/api/v1/videos', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify({
        maxResultCount: 3,
        skipCount: 0,
        sorting: 'VideoAddTime desc'
      })
    });

    const responseData = await response.json();
    console.log('1. API调用成功');

    if (responseData.code === 200 && responseData.data) {
      console.log('2. 开始解密数据...');
      const decryptedString = decrypt(responseData.data);
      const videoData = JSON.parse(decryptedString);

      console.log('3. 解密成功！');

      console.log('4. 视频数据结构:', typeof videoData);
      console.log('5. 视频数据内容预览:', JSON.stringify(videoData).substring(0, 200) + '...');

      // 检查不同的数据结构
      let items = [];
      if (videoData && videoData.Items) {
        items = videoData.Items;
        console.log('6. 使用Items字段，数量:', items.length);
      } else if (videoData && videoData.items) {
        items = videoData.items;
        console.log('6. 使用items字段，数量:', items.length);
      } else if (Array.isArray(videoData)) {
        items = videoData;
        console.log('6. 直接使用数组，数量:', items.length);
      } else {
        console.log('6. 未知的数据结构');
        return;
      }

      if (items.length > 0) {
        console.log('7. 找到视频数据，数量:', items.length);

        // 模拟页面数据处理
        console.log('\n8. 模拟页面数据处理...');
        const processedData = processVideoData(items.slice(0, 3));

        console.log('9. 数据处理完成');
        console.log('处理后的数据示例:');
        console.log('- 第一个视频的poster字段:', processedData[0].poster);
        console.log('- 第一个视频的title字段:', processedData[0].title);
        console.log('- 第一个视频的score字段:', processedData[0].score);

        // 测试组件图片显示逻辑
        testImageDisplay(processedData);

        console.log('\n✅ 最终图片显示修复测试完成！');
        console.log('现在所有页面和组件都应该能正确显示图片了。');

        // 总结修复内容
        console.log('\n=== 修复总结 ===');
        console.log('1. ✅ 修复了API数据解密功能');
        console.log('2. ✅ 修复了字段名大小写兼容性');
        console.log('3. ✅ 统一了数据处理流程');
        console.log('4. ✅ 修复了组件图片显示逻辑');
        console.log('5. ✅ 确保了数据流的一致性');

      } else {
        console.log('7. 未找到视频数据');
      }
    } else {
      console.log('2. API返回错误或无数据');
    }

  } catch (error) {
    console.error('测试失败:', error.message);
  }
}

// 运行测试
testFinalImageFix().then(() => {
  console.log('\n=== 测试完成 ===');
});
