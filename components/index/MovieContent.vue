<template>
  <view class="box">
    <ul class="search-bar">
      <li @click="switchCategory('all')" :class="{ active: currentCategory === 'all' }">
        <view>
          <image src="/static/quanbu.png" mode="widthFix"/>
        </view>
        <text>全部</text>
      </li>
      <li @click="switchCategory('latest')" :class="{ active: currentCategory === 'latest' }">
        <view>
          <image src="/static/shangchuan.png" mode="widthFix"/>
        </view>
        <text>最新上传</text>
      </li>
      <li @click="switchCategory('popular')" :class="{ active: currentCategory === 'popular' }">
        <view>
          <image src="/static/renqi1.png" mode="widthFix"/>
        </view>
        <text>人气高</text>
      </li>
      <li @click="switchCategory('rating')" :class="{ active: currentCategory === 'rating' }">
        <view>
          <image src="/static/pingfen.png" mode="widthFix"/>
        </view>
        <text>评分高</text>
      </li>
      <li @click="switchCategory('updated')" :class="{ active: currentCategory === 'updated' }">
        <view>
          <image src="/static/gengxin.png" mode="widthFix"/>
        </view>
        <text>最近更新</text>
      </li>
    </ul>

    <!-- 加载中提示 -->
    <view class="loading-container" v-if="loading">
      <view class="loading-text">加载中...</view>
    </view>

    <!-- 错误提示 -->
    <view class="error-container" v-else-if="error">
      <view class="error-text">{{ error }}</view>
      <view class="retry-button" @click="loadVideos">重试</view>
    </view>

    <!-- 视频列表 -->
    <view v-else>
      <ul class="list video">
        <li v-for="(item, index) in videos" :key="index">
          <navigator :url="'/pages/view/view?id=' + item.id">
            <view class="cover">
              <image :src="item.poster || 'https://placehold.co/300x400'" mode="aspectFill"/>
              <view class="video-info">
                <view class="pingfen" v-if="item.score">{{ item.score }}</view>
                <view class="renqitime">
                  <span v-if="item.hits">{{ item.hits }}</span>
                  <span v-if="item.videoDuration || item.remarks">{{ item.videoDuration || item.remarks || '' }}</span>
                </view>
              </view>
            </view>
            <view class="biaoti">{{ item.title }}</view>
            <view class="tags" v-if="item.tags && item.tags.length">
              {{ item.tags.slice(0, 2).join(' ') }}
            </view>
          </navigator>
        </li>
      </ul>

      <!-- 无数据提示 -->
      <view class="no-data" v-if="videos.length === 0">
        <text>暂无数据</text>
      </view>

      <!-- 加载状态提示 -->
      <view class="load-more" v-if="videos.length > 0">
        <view v-if="loadingMore" class="loading">加载中...</view>
        <view v-else-if="!hasMore" class="no-more">没有更多数据了</view>
      </view>
    </view>

  </view>
</template>

<script>
import { getVideoList } from '@/api/video';
import logger from '@/utils/logger';

export default {
  name: 'MovieContent',
  props: {
    // 视频类型，如电影、电视剧等
    videoType: {
      type: String,
      default: null
    },
    // 视频类型ID，用于加载标签数据
    videoTypeId: {
      type: String,
      default: null
    },
    // 视频标签
    videoTag: {
      type: String,
      default: null
    }
  },
  // 组件生命周期函数
  mounted() {
    // 添加事件监听器
    logger.info('MovieContent: 添加事件监听器');
    // 先移除可能存在的监听器，避免重复
    uni.$off('page-reach-bottom', this.handleReachBottom);
    uni.$off('page-pull-down-refresh', this.handlePullDownRefresh);
    // 监听页面滚动事件
    uni.$on('page-reach-bottom', this.handleReachBottom);
    // 监听页面下拉刷新事件
    uni.$on('page-pull-down-refresh', this.handlePullDownRefresh);
  },

  beforeDestroy() {
    // 移除监听
    logger.info('MovieContent: 移除事件监听器');
    uni.$off('page-reach-bottom', this.handleReachBottom);
    uni.$off('page-pull-down-refresh', this.handlePullDownRefresh);
  },
  data() {
    return {
      videos: [],
      loading: true,
      error: null,
      currentCategory: 'all', // 当前选中的分类
      currentPage: 1, // 当前页码
      hasMore: true, // 是否还有更多数据
      loadingMore: false, // 是否正在加载更多
      pageSize: 12, // 每页数量
      dataLoaded: false, // 标记数据是否已加载
      categoryMap: {
        all: { // 全部
          sorting: 'VideoAddTime desc', // 按添加时间降序排序
          videoTag: null,
          isRecommended: null,
          title: '全部'
        },
        latest: { // 最新上传
          sorting: 'VideoAddTime desc', // 按添加时间降序排序
          videoTag: null,
          isRecommended: null,
          title: '最新上传'
        },
        popular: { // 人气高
          sorting: 'VideoHits desc', // 按点击量降序排序
          videoTag: null,
          isRecommended: null,
          title: '人气高'
        },
        rating: { // 评分高
          sorting: 'VideoScore desc', // 按评分降序排序
          videoTag: null,
          isRecommended: null,
          title: '评分高'
        },
        updated: { // 最近更新
          sorting: 'VideoUpdateTime desc', // 按更新时间降序排序
          videoTag: null,
          isRecommended: null,
          title: '最近更新'
        }
      }
    };
  },

  created() {
    // 在created钩子中不加载数据，避免与activated钩子重复加载
    // 数据加载将在activated钩子中进行
    logger.info('MovieContent组件创建');
  },

  // 组件被keep-alive缓存时，再次激活时触发
  activated() {
    logger.info('MovieContent组件被激活，当前videoType:', this.videoType);

    // 重新添加事件监听器，确保只有一个监听器实例
    uni.$off('page-reach-bottom', this.handleReachBottom);
    uni.$off('page-pull-down-refresh', this.handlePullDownRefresh);
    uni.$on('page-reach-bottom', this.handleReachBottom);
    uni.$on('page-pull-down-refresh', this.handlePullDownRefresh);

    // 强制重新加载数据，无论是否有数据
    // 重置所有状态
    this.currentCategory = 'all';
    this.videos = [];
    this.currentPage = 1;
    this.hasMore = true;
    this.loading = true;
    this.loadingMore = false;
    this.dataLoaded = false;
    // 强制重新加载数据
    this.loadVideos();
  },

  // 组件被keep-alive缓存时，即将切换出去时触发
  deactivated() {
    logger.info('MovieContent组件即将切换出去');
    // 移除事件监听器，避免内存泄漏
    uni.$off('page-reach-bottom', this.handleReachBottom);
    uni.$off('page-pull-down-refresh', this.handlePullDownRefresh);
  },

  // 监听属性变化
  watch: {
    // 监听 videoType 变化，重新加载数据
    videoType(newVal, oldVal) {
      // 修复：当videoType发生任何变化时都重新加载数据
      if (newVal !== oldVal) {
        logger.info(`videoType变化: ${oldVal} -> ${newVal}，重新加载数据`);
        // 重置当前分类为全部
        this.currentCategory = 'all';
        // 清空视频数据
        this.videos = [];
        // 重置页码和加载状态
        this.currentPage = 1;
        this.hasMore = true;
        this.loading = true;
        this.loadingMore = false;
        // 重新加载视频数据
        this.loadVideos();
      }
    }
  },
  methods: {
    /**
     * 加载视频数据
     * @param {boolean} isLoadMore - 是否是加载更多
     */
    async loadVideos(isLoadMore = false) {
      logger.info(`开始加载视频数据，isLoadMore=${isLoadMore}, videoType=${this.videoType}, 当前数据量=${this.videos.length}`);

      // 如果正在加载更多数据且没有更多数据，则返回
      if (isLoadMore && this.loadingMore) {
        logger.info('正在加载更多数据，不重复加载');
        return;
      }

      if (isLoadMore && !this.hasMore) {
        logger.info('没有更多数据了，不再加载');
        return;
      }

      // 已在上面判断了加载状态，这里不再重复判断

      try {
        // 设置加载状态
        if (isLoadMore) {
          this.loadingMore = true;
        } else {
          this.loading = true;
          this.currentPage = 1; // 重置页码
          this.hasMore = true; // 重置是否有更多数据
        }

        this.error = null;

        // 获取当前分类的参数
        const categoryConfig = this.categoryMap[this.currentCategory] || this.categoryMap.all;

        // 直接使用 getVideoList 获取视频列表
        const apiParams = {
          page: isLoadMore ? this.currentPage + 1 : 1,
          pageSize: this.pageSize,
          videoType: this.videoType,
          sorting: categoryConfig.sorting || 'VideoAddTime desc', // 使用 VideoAddTime desc 作为默认排序
          videoTag: categoryConfig.videoTag || this.videoTag,
          isRecommended: categoryConfig.isRecommended
        };

        const response = await getVideoList(apiParams);

        // 检查 response 是否为数组或包含 Items/items 属性
        if ((Array.isArray(response) && response.length > 0) || (response && (response.Items || response.items))) {
          // 处理视频数据，兼容大小写字段名
          const items = Array.isArray(response) ? response : (response.Items || response.items);
          const newVideos = items.map(item => ({
            id: item.Id || item.id,
            videoId: item.VideoId || item.videoId,
            title: item.VideoName || item.videoName,
            year: (item.VideoYear || item.videoYear) || ((item.VideoAddTime || item.videoAddTime) ? new Date(item.VideoAddTime || item.videoAddTime).getFullYear() + '年' : ''),
            tags: (item.VideoTag || item.videoTag) ? (item.VideoTag || item.videoTag).split(',') : [],
            poster: (item.VideoPic || item.videoPic) || 'https://placehold.co/300x400',
            score: item.VideoScore || item.videoScore,
            hits: item.VideoHits || item.videoHits,
            actor: item.VideoActor || item.videoActor,
            area: item.VideoArea || item.videoArea,
            lang: item.VideoLang || item.videoLang,
            director: item.VideoDirector || item.videoDirector,
            blurb: item.VideoBlurb || item.videoBlurb,
            remarks: item.VideoRemarks || item.videoRemarks,
            videoDuration: item.VideoDuration || item.videoDuration
          }));

          // 判断是否还有更多数据
          this.hasMore = newVideos.length === this.pageSize;

          // 更新页码和视频数据
          if (isLoadMore && newVideos.length > 0) {
            this.currentPage++;
            // 合并数据
            this.videos = [...this.videos, ...newVideos];
          } else {
            // 首次加载或切换分类时替换数据
            this.videos = newVideos;
          }

        } else {
          if (!isLoadMore) {
            this.videos = [];
          }
          this.hasMore = false;
        }
      } catch (error) {
        this.error = '加载视频失败，请重试';
      } finally {
        if (isLoadMore) {
          this.loadingMore = false;
        } else {
          this.loading = false;
        }
      }
    },

    /**
     * 切换分类
     * @param {string} category - 分类标识
     */
    switchCategory(category) {
      if (this.currentCategory === category) return; // 避免重复加载

      // 更新当前分类
      this.currentCategory = category;

      // 清空视频数据，避免数据累加
      this.videos = [];

      // 重置页码和加载状态
      this.currentPage = 1;
      this.hasMore = true;
      this.loading = true;
      this.loadingMore = false;

      // 获取当前分类的配置
      const categoryConfig = this.categoryMap[category] || this.categoryMap.all;

      // 构建跳转参数
      let url = `/pages/list/list?category=${category}&videoType=${this.videoType || ''}&sorting=${categoryConfig.sorting || ''}&title=${categoryConfig.title || ''}`;

      // 添加页面标题参数
      // 使用父组件传入的videoType作为页面标题（如电影、动漫、连续剧等）
      if (this.videoType) {
        url += `&pageTitle=${encodeURIComponent(this.videoType)}`;
      }

      // 添加videoTypeId参数，用于加载标签数据
      if (this.videoTypeId) {
        url += `&videoTypeId=${encodeURIComponent(this.videoTypeId)}`;
      }

      // 跳转到list页面
      // 注意：不在跳转前加载数据，避免重复调用接口
      uni.navigateTo({
        url: url
      });
    },

    /**
     * 加载更多数据
     */
    loadMore() {
      if (this.hasMore && !this.loadingMore) {
        this.loadVideos(true);
      }
    },

    /**
     * 处理页面触底事件
     */
    handleReachBottom() {
      this.loadMore();
    },

    /**
     * 处理下拉刷新事件
     */
    handlePullDownRefresh() {
      this.currentPage = 1;
      this.hasMore = true;
      this.loadVideos().then(() => {
        // 停止下拉刷新
        uni.stopPullDownRefresh();
      });
    }
  }
}
</script>

<style scoped>
.category-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
  color: #fff;
  display: flex;
  align-items: center;

  .subtitle {
    font-size: 28rpx;
    font-weight: normal;
    margin-left: 10rpx;
    color: #fe748e;
  }
}

.search-bar {
  display: flex;
  justify-content: space-between;
  gap: 33rpx;
  margin-bottom: 40rpx;

  li {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    font-size: 25rpx;
    cursor: pointer;

    &.active {
      view {
        background-color: #fe748e;
      }
      text {
        color: #fe748e;
      }
    }

    view {
      height: 64rpx;
      border: 1px solid #fe748e;
      width: calc(100% - 2px);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 20rpx;
      border-radius: 5rpx;
      transition: all 0.3s ease;
    }

    text {
      display: block;
      text-align: center;
      transition: color 0.3s ease;
    }

    image {
      width: 34rpx;
    }
  }
}

.loading-container, .error-container, .no-data {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200rpx;
  width: 100%;
  flex-direction: column;
}

.loading-text, .error-text {
  font-size: 28rpx;
  color: #999;
}

.retry-button {
  margin-top: 20rpx;
  padding: 10rpx 30rpx;
  background-color: #fe748e;
  color: #fff;
  border-radius: 5rpx;
  font-size: 24rpx;
}

.no-data {
  color: #999;
  font-size: 28rpx;
}

/* 加载状态提示相关样式 */
.load-more {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 80rpx;
  margin: 10rpx 0;
}

.loading, .no-more {
  color: #999;
  font-size: 28rpx;
}
</style>