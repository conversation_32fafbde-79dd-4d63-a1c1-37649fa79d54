<template>
  <view class="foot">
    <view><span>广告联系: </span> <span><image src="/static/tg.png" /></span> <span>@aaaaaaaaaa </span> <span>|</span> <span><image src="/static/tg.png" style="filter: hue-rotate(90deg)" /></span> <span>官方交流群:@bbbbbbbbbb</span></view>
    <view>永久域名 : aaaaa.com</view>
  </view>
</template>

<script>
export default {
  name: "foot.vue"
}
</script>

<style scoped>
.foot{
  color: #fff;
  font-size: 24rpx;
  view{
    display: flex;
    justify-content: center;
  }
  image{
    width: 40rpx;
    height: 40rpx;
  }
}

</style>