<template>
  <view class="laoji">
    <view class="text">
      <span>回家邮箱:</span>
      <span>{{ systemSetting.contactEmail }}</span>
      <span @click="copyEmail">复制</span>
    </view>
    <view class="text1">
      <span @click="downloadQRCode">点击下载回家二维码</span>
    </view>
  </view>
</template>

<script>
import { ref, onMounted } from 'vue'
import { getCachedSystemSetting } from '@/api/systemSetting'
import { generateQRCodeForUniApp } from '@/utils/qrcode'
import logger from '@/utils/logger'

export default {
  name: "laoji",
  setup() {
    // 响应式数据
    const systemSetting = ref({
      domain: 'aaa.com',
      contactEmail: '<EMAIL>'
    })
    const qrCodeBase64 = ref('')
    const loadingQRCode = ref(false)

    /**
     * 加载系统设置
     */
    const loadSystemSetting = async () => {
      try {
        logger.log('[老鸡组件] 开始加载系统设置')

        const setting = await getCachedSystemSetting()

        if (setting) {
          systemSetting.value = {
            domain: setting.domain || 'aaa.com',
            contactEmail: setting.contactEmail || '<EMAIL>'
          }
          logger.log('[老鸡组件] 系统设置加载成功:', systemSetting.value)
        } else {
          logger.warn('[老鸡组件] 未获取到系统设置数据，使用默认值')
        }
      } catch (error) {
        logger.error('[老鸡组件] 加载系统设置失败:', error)
        // 使用默认值
        systemSetting.value = {
          domain: 'aaa.com',
          contactEmail: '<EMAIL>'
        }
      }
    }

    /**
     * 复制邮箱地址
     */
    const copyEmail = () => {
      try {
        uni.setClipboardData({
          data: systemSetting.value.contactEmail,
          success: () => {
            uni.showToast({
              title: '邮箱已复制',
              icon: 'success'
            })
            logger.log('[老鸡组件] 邮箱复制成功:', systemSetting.value.contactEmail)
          },
          fail: (err) => {
            logger.error('[老鸡组件] 复制邮箱失败:', err)
            uni.showToast({
              title: '复制失败，请重试',
              icon: 'none'
            })
          }
        })
      } catch (error) {
        logger.error('[老鸡组件] 复制邮箱时出错:', error)
        uni.showToast({
          title: '复制失败，请重试',
          icon: 'none'
        })
      }
    }

    /**
     * 生成二维码
     */
    const generateQRCode = async () => {
      try {
        loadingQRCode.value = true
        logger.log('[老鸡组件] 开始生成二维码，域名:', systemSetting.value.domain)

        const qrCodeData = await generateQRCodeForUniApp(systemSetting.value.domain, {
          size: 200,
          backgroundColor: '#FFFFFF',
          foregroundColor: '#000000',
          autoFormat: true
        })

        qrCodeBase64.value = qrCodeData
        logger.log('[老鸡组件] 二维码生成成功')
        return qrCodeData
      } catch (error) {
        logger.error('[老鸡组件] 生成二维码失败:', error)
        uni.showToast({
          title: '二维码生成失败',
          icon: 'none'
        })
        throw error
      } finally {
        loadingQRCode.value = false
      }
    }

    /**
     * 下载二维码
     */
    const downloadQRCode = async () => {
      try {
        // 显示加载提示
        uni.showLoading({
          title: '生成中...'
        })

        // 如果还没有生成二维码，先生成
        let qrCodeData = qrCodeBase64.value
        if (!qrCodeData) {
          qrCodeData = await generateQRCode()
        }

        uni.hideLoading()

        if (!qrCodeData) {
          uni.showToast({
            title: '二维码生成失败',
            icon: 'none'
          })
          return
        }

        // #ifdef H5
        // H5环境下使用下载链接
        const link = document.createElement('a')
        link.href = qrCodeData
        link.download = '回家二维码.png'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)

        uni.showToast({
          title: '二维码已保存',
          icon: 'success'
        })
        logger.log('[老鸡组件] H5环境二维码下载成功')
        // #endif

        // #ifdef APP-PLUS
        // App环境下保存到相册
        uni.saveImageToPhotosAlbum({
          filePath: qrCodeData,
          success: () => {
            uni.showToast({
              title: '二维码已保存到相册',
              icon: 'success'
            })
            logger.log('[老鸡组件] App环境二维码保存成功')
          },
          fail: (error) => {
            logger.error('[老鸡组件] App环境保存二维码失败:', error)
            uni.showToast({
              title: '保存失败',
              icon: 'none'
            })
          }
        })
        // #endif

        // #ifdef MP
        // 小程序环境下保存到相册
        uni.saveImageToPhotosAlbum({
          filePath: qrCodeData,
          success: () => {
            uni.showToast({
              title: '二维码已保存到相册',
              icon: 'success'
            })
            logger.log('[老鸡组件] 小程序环境二维码保存成功')
          },
          fail: (error) => {
            logger.error('[老鸡组件] 小程序环境保存二维码失败:', error)
            uni.showToast({
              title: '保存失败，请手动保存',
              icon: 'none'
            })
          }
        })
        // #endif

      } catch (error) {
        uni.hideLoading()
        logger.error('[老鸡组件] 下载二维码失败:', error)
        uni.showToast({
          title: '下载失败',
          icon: 'none'
        })
      }
    }

    // 组件挂载时加载系统设置
    onMounted(() => {
      loadSystemSetting()
    })

    return {
      systemSetting,
      loadingQRCode,
      copyEmail,
      downloadQRCode
    }
  }
}
</script>

<style scoped>
.laoji {
  padding: 20rpx;
  text-align: center;
  margin-bottom: 20rpx;
  line-height: 180%;
  text-align: center;
}

.text {
  color: #fff;
  font-size: 28rpx;
  gap: 20rpx;
  display: flex;
  justify-content: center;


  span:last-child{
    color: #fe748e;
  }

}
.text1 {
  color: yellow;
  font-size: 28rpx;
  gap: 20rpx;
  display: flex;
  justify-content: center;

}
</style>