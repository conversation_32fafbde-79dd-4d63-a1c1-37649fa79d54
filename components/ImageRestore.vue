<template>
  <!-- H5环境使用img标签，完全参考PC端实现 -->
  <!-- #ifdef H5 -->
  <img ref="imageRef" :src="fullImageUrl" :alt="alt" style="width: 100%; height: 100%; object-fit: cover;" />
  <!-- #endif -->

  <!-- 非H5环境使用image标签 -->
  <!-- #ifndef H5 -->
  <image
    ref="imageRef"
    :src="processedImageUrl || imageUrl"
    mode="aspectFill"
    :alt="alt"
    style="width: 100%; height: 100%;"
    @error="handleImageError"
    @load="handleImageLoad"
  />
  <!-- #endif -->
</template>

<script>
export default {
  name: 'ImageRestore',
  props: {
    imageUrl: {
      type: String,
      required: true
    },
    alt: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      processedImageUrl: ''
    }
  },
  computed: {
    fullImageUrl() {
      if (!this.imageUrl) return '';

      // 如果已经是完整URL，直接返回
      if (this.imageUrl.startsWith('http://') || this.imageUrl.startsWith('https://')) {
        return this.imageUrl;
      }

      // 如果是相对路径，拼接当前域名
      const baseUrl = window.location.origin;
      return baseUrl + this.imageUrl;
    }
  },
  mounted() {
    console.log('[ImageRestore] 组件挂载, imageUrl:', this.imageUrl);
    console.log('[ImageRestore] $refs.imageRef:', this.$refs.imageRef);

    // 非H5环境下先设置原始图片，确保能立即显示
    // #ifndef H5
    if (this.imageUrl) {
      console.log('[ImageRestore] 非H5环境，设置初始图片URL:', this.imageUrl);
      this.processedImageUrl = this.imageUrl;
    }
    // #endif

    if (this.imageUrl && this.$refs.imageRef) {
      console.log('[ImageRestore] 开始处理图片');
      console.log('[ImageRestore] 完整URL:', this.fullImageUrl);
      this.fetchImageAndRemoveFirstFourBytes(this.fullImageUrl, this.$refs.imageRef);
    } else {
      console.warn('[ImageRestore] 缺少必要参数:', { imageUrl: this.imageUrl, imageRef: this.$refs.imageRef });
    }
  },
  watch: {
    imageUrl(newUrl) {
      if (newUrl && this.$refs.imageRef) {
        // 清理之前的处理结果
        // #ifndef H5
        this.processedImageUrl = newUrl;
        // #endif
        this.fetchImageAndRemoveFirstFourBytes(newUrl, this.$refs.imageRef);
      }
    }
  },
  methods: {
    async fetchImageAndRemoveFirstFourBytes(imageUrl, imgElement) {
      console.log('[ImageRestore] 开始处理图片:', imageUrl);
      console.log('[ImageRestore] 图片元素:', imgElement);

      if (!imageUrl || !imgElement) {
        console.warn('[ImageRestore] 参数不完整:', { imageUrl, imgElement });
        return;
      }

      try {
        // #ifdef H5
        // H5环境下使用与PC端完全相同的处理逻辑
        console.log('[ImageRestore] H5环境，使用fetch处理图片');
        const response = await fetch(imageUrl, { cache: 'default' });
        console.log('[ImageRestore] Fetch响应:', response.status, response.statusText);

        if (!response.ok) {
          throw new Error(`无法获取图片: ${response.status} ${response.statusText}`);
        }

        const buffer = await response.arrayBuffer();
        console.log('[ImageRestore] 原始图片大小:', buffer.byteLength);

        // 检查图片是否需要处理（检查前4个字节）
        const firstFourBytes = new Uint8Array(buffer.slice(0, 4));
        console.log('[ImageRestore] 前4个字节:', Array.from(firstFourBytes).map(b => b.toString(16).padStart(2, '0')).join(' '));

        // 检查是否为正常的图片文件头
        const isJPEG = firstFourBytes[0] === 0xFF && firstFourBytes[1] === 0xD8;
        const isPNG = firstFourBytes[0] === 0x89 && firstFourBytes[1] === 0x50 && firstFourBytes[2] === 0x4E && firstFourBytes[3] === 0x47;
        const isGIF = firstFourBytes[0] === 0x47 && firstFourBytes[1] === 0x49 && firstFourBytes[2] === 0x46;
        const isWebP = firstFourBytes[0] === 0x52 && firstFourBytes[1] === 0x49 && firstFourBytes[2] === 0x46 && firstFourBytes[3] === 0x46;

        const isNormalImage = isJPEG || isPNG || isGIF || isWebP;
        console.log('[ImageRestore] 图片格式检测:', { isJPEG, isPNG, isGIF, isWebP, isNormalImage });

        let finalBuffer;
        if (isNormalImage) {
          console.log('[ImageRestore] 检测到正常图片格式，不需要处理');
          finalBuffer = buffer;
        } else {
          console.log('[ImageRestore] 非正常图片格式，移除前4个字节');
          finalBuffer = buffer.slice(4);
        }

        console.log('[ImageRestore] 最终图片大小:', finalBuffer.byteLength);

        const blob = new Blob([finalBuffer], { type: response.headers.get('content-type') || 'image/jpeg' });
        const objectURL = URL.createObjectURL(blob);
        console.log('[ImageRestore] 生成blob URL:', objectURL);

        imgElement.src = objectURL;
        imgElement.onload = () => {
          console.log('[ImageRestore] ✅ 处理后的图片加载成功');
          URL.revokeObjectURL(objectURL);
        };
        imgElement.onerror = () => {
          console.error('[ImageRestore] ❌ 处理后的图片加载失败，回退到原始图片');
          console.log('[ImageRestore] 原始图片URL:', imageUrl);

          // 清理blob URL
          URL.revokeObjectURL(objectURL);

          // 回退到原始图片，但不再设置src，因为原始图片已经在初始化时设置了
          console.log('[ImageRestore] 使用初始设置的原始图片');
        };
        // #endif

        // #ifndef H5
        // 非H5环境下，尝试使用uni.request处理图片
        const self = this;
        uni.request({
          url: imageUrl,
          method: 'GET',
          responseType: 'arraybuffer',
          success(res) {
            try {
              if (res.statusCode === 200 && res.data) {
                // 移除前4个字节
                const buffer = res.data;
                const newBuffer = buffer.slice(4);

                // 在非H5环境下，将ArrayBuffer转换为base64
                const base64 = uni.arrayBufferToBase64(newBuffer);
                const dataUrl = `data:image/jpeg;base64,${base64}`;

                self.processedImageUrl = dataUrl;
              } else {
                console.warn('图片请求失败，使用原始图片');
                self.processedImageUrl = imageUrl;
              }
            } catch (error) {
              console.error('处理图片数据失败:', error);
              self.processedImageUrl = imageUrl;
            }
          },
          fail(error) {
            console.error('图片请求失败:', error);
            self.processedImageUrl = imageUrl;
          }
        });
        // #endif

      } catch (error) {
        console.error('[ImageRestore] 处理图片时发生错误:', error);
        console.log('[ImageRestore] 回退到原始图片显示');

        // #ifdef H5
        // H5环境下直接设置原始图片
        if (imgElement) {
          imgElement.src = imageUrl;
        }
        // #endif

        // #ifndef H5
        this.processedImageUrl = imageUrl;
        // #endif
      }
    },

    // 图片加载成功
    handleImageLoad() {
      console.log('[ImageRestore] ✅ 图片加载成功:', this.imageUrl);
    },

    // 图片加载失败
    handleImageError(e) {
      console.error('[ImageRestore] ❌ 图片加载失败:', this.imageUrl, e);
      // 在非H5环境下，如果处理后的图片加载失败，尝试使用原始图片
      // #ifndef H5
      if (this.processedImageUrl !== this.imageUrl) {
        console.log('回退到原始图片URL');
        this.processedImageUrl = this.imageUrl;
      }
      // #endif
    }
  }
}
</script>

<style scoped></style>
