<template>
  <!-- H5环境使用img标签，参考PC端实现 -->
  <!-- #ifdef H5 -->
  <img ref="imageRef" :src="fullImageUrl" v-bind="$attrs" :alt="alt" style="width: 100%; height: 100%; object-fit: cover;" />
  <!-- #endif -->

  <!-- 非H5环境使用image标签 -->
  <!-- #ifndef H5 -->
  <image
    ref="imageRef"
    :src="processedImageUrl || fullImageUrl"
    mode="aspectFill"
    :alt="alt"
    style="width: 100%; height: 100%;"
    @error="handleImageError"
    @load="handleImageLoad"
  />
  <!-- #endif -->
</template>

<script>
export default {
  name: 'ImageRestore',
  props: {
    imageUrl: {
      type: String,
      required: true
    },
    alt: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      processedImageUrl: ''
    }
  },
  computed: {
    fullImageUrl() {
      if (!this.imageUrl) return '';

      // 如果已经是完整URL，直接返回
      if (this.imageUrl.startsWith('http://') || this.imageUrl.startsWith('https://')) {
        return this.imageUrl;
      }

      // 如果是相对路径，拼接API域名
      // #ifdef H5
      const baseUrl = window.location.origin;
      // #endif
      // #ifndef H5
      const baseUrl = 'https://api.player.4ii.cc';
      // #endif
      return baseUrl + this.imageUrl;
    }
  },
  mounted() {
    // 非H5环境下先设置原始图片，确保能立即显示
    // #ifndef H5
    if (this.imageUrl) {
      this.processedImageUrl = this.fullImageUrl;
    }
    // #endif

    if (this.imageUrl && this.$refs.imageRef) {
      this.fetchImageAndRemoveFirstFourBytes(this.fullImageUrl, this.$refs.imageRef);
    }
  },
  watch: {
    imageUrl(newUrl) {
      if (newUrl && this.$refs.imageRef) {
        // #ifndef H5
        this.processedImageUrl = this.fullImageUrl;
        // #endif
        this.fetchImageAndRemoveFirstFourBytes(this.fullImageUrl, this.$refs.imageRef);
      }
    }
  },
  methods: {
    async fetchImageAndRemoveFirstFourBytes(imageUrl, imgElement) {
      if (!imageUrl || !imgElement) {
        return;
      }

      try {
        // #ifdef H5
        // H5环境下使用与PC端完全相同的处理逻辑
        const response = await fetch(imageUrl, { cache: 'default' });
        if (!response.ok) {
          throw new Error(`无法获取图片: ${response.status} ${response.statusText}`);
        }

        // 将响应转换为ArrayBuffer
        const buffer = await response.arrayBuffer();

        // 创建一个新的ArrayBuffer，排除前四个字节
        const newBuffer = buffer.slice(4);

        // 将处理后的数据转换为Blob
        const blob = new Blob([newBuffer], { type: response.headers.get('content-type') || 'image/jpeg' });

        // 创建URL并设置到img元素的src属性
        const objectURL = URL.createObjectURL(blob);
        imgElement.src = objectURL;

        // 当图片加载完成后，释放创建的URL对象
        imgElement.onload = () => {
          URL.revokeObjectURL(objectURL);
        };
        // #endif

        // #ifndef H5
        // 非H5环境下，使用uni.request处理图片
        const self = this;
        uni.request({
          url: imageUrl,
          method: 'GET',
          responseType: 'arraybuffer',
          success(res) {
            try {
              if (res.statusCode === 200 && res.data) {
                // 移除前4个字节
                const buffer = res.data;
                const newBuffer = buffer.slice(4);

                // 在非H5环境下，将ArrayBuffer转换为base64
                const base64 = uni.arrayBufferToBase64(newBuffer);
                const dataUrl = `data:image/jpeg;base64,${base64}`;

                self.processedImageUrl = dataUrl;
              } else {
                self.processedImageUrl = self.fullImageUrl;
              }
            } catch (error) {
              self.processedImageUrl = self.fullImageUrl;
            }
          },
          fail(error) {
            self.processedImageUrl = self.fullImageUrl;
          }
        });
        // #endif

      } catch (error) {
        // #ifdef H5
        if (imgElement) {
          imgElement.src = imageUrl;
        }
        // #endif

        // #ifndef H5
        this.processedImageUrl = this.fullImageUrl;
        // #endif
      }
    },

    // 图片加载成功
    handleImageLoad() {
      // 图片加载成功
    },

    // 图片加载失败
    handleImageError(e) {
      // 在非H5环境下，如果处理后的图片加载失败，尝试使用原始图片
      // #ifndef H5
      if (this.processedImageUrl !== this.fullImageUrl) {
        this.processedImageUrl = this.fullImageUrl;
      }
      // #endif
    }
  }
}
</script>

<style scoped></style>
