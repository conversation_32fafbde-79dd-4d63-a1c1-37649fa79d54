<!DOCTYPE html>
<html>
<head>
    <title>直接图片测试</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-image { width: 200px; height: 150px; border: 1px solid #ddd; margin: 10px; }
        .success { color: green; }
        .error { color: red; }
    </style>
</head>
<body>
    <h1>直接图片访问测试</h1>
    
    <h3>测试图片URL（从日志中获取）：</h3>
    <div id="imageContainer"></div>
    
    <script>
        // 从日志中获取的图片URL
        const testUrls = [
            'http://localhost:8080/api/v1/image/video/3a1a4839f21a0e147758b6b2562a73d6',
            'http://localhost:8080/api/v1/image/video/3a1a48396ff4c8168c660037b406d1ee',
            'http://localhost:8080/api/v1/image/video/3a1a483a17f89121f1617c71e492a791'
        ];
        
        const container = document.getElementById('imageContainer');
        
        testUrls.forEach((url, index) => {
            const div = document.createElement('div');
            div.style.margin = '20px 0';
            div.style.border = '1px solid #ccc';
            div.style.padding = '10px';
            
            const title = document.createElement('h4');
            title.textContent = `测试图片 ${index + 1}`;
            div.appendChild(title);
            
            const urlDiv = document.createElement('div');
            urlDiv.textContent = `URL: ${url}`;
            urlDiv.style.fontSize = '12px';
            urlDiv.style.color = '#666';
            urlDiv.style.wordBreak = 'break-all';
            div.appendChild(urlDiv);
            
            const img = document.createElement('img');
            img.className = 'test-image';
            img.src = url;
            
            const status = document.createElement('div');
            status.textContent = '加载中...';
            
            img.onload = () => {
                status.innerHTML = '<span class="success">✅ 加载成功</span>';
                console.log('图片加载成功:', url);
            };
            
            img.onerror = (e) => {
                status.innerHTML = '<span class="error">❌ 加载失败</span>';
                console.error('图片加载失败:', url, e);
            };
            
            div.appendChild(img);
            div.appendChild(status);
            container.appendChild(div);
        });
        
        // 测试fetch请求
        async function testFetch() {
            const testUrl = testUrls[0];
            console.log('测试fetch请求:', testUrl);
            
            try {
                const response = await fetch(testUrl);
                console.log('Fetch响应状态:', response.status, response.statusText);
                console.log('响应头:', [...response.headers.entries()]);
                
                if (response.ok) {
                    const buffer = await response.arrayBuffer();
                    console.log('图片数据大小:', buffer.byteLength);
                    
                    // 检查前几个字节
                    const firstBytes = new Uint8Array(buffer.slice(0, 10));
                    console.log('前10个字节:', Array.from(firstBytes).map(b => b.toString(16).padStart(2, '0')).join(' '));
                }
            } catch (error) {
                console.error('Fetch请求失败:', error);
            }
        }
        
        // 页面加载完成后测试fetch
        setTimeout(testFetch, 2000);
    </script>
</body>
</html>
