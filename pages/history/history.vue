<template>
  <view class="container">
    <view class="content-area">
      <view class="subnav">
        <ul>
          <li :class="{ active: activeTab === 'movie' }" @click="switchTab('movie')">剧集</li>
          <li :class="{ active: activeTab === 'xiaomovie' }" @click="switchTab('xiaomovie')">小视频</li>
        </ul>
      </view>

      <view class="content">
        <view class="movie-list" v-show="activeTab === 'movie'">
          <!-- 加载中提示 -->
          <view class="loading-container" v-if="loading">
            <view class="loading-spinner"></view>
            <text class="loading-text">正在加载历史记录...</text>
          </view>

          <!-- 无数据提示 -->
          <view class="empty-container" v-else-if="movieHistory.length === 0">
            <view class="empty-icon">📺</view>
            <text class="empty-text">暂无观看历史</text>
          </view>

          <!-- 历史记录列表 -->
          <view class="movie-card" v-else v-for="(item, index) in movieHistory" :key="index" @click="navigateToVideo(item)">
            <view class="cover">
              <image :src="item.videoPic || 'https://placehold.co/300x400'" mode="widthFix"/>
            </view>
            <view class="info">
              <h3 class="movie-title">{{ item.videoName }}</h3>
              <view class="fabu">{{ formatDate(item.timestamp) }}</view>
              <view class="zhuyan" v-if="item.currentEpisode">当前集数：{{ item.currentEpisode }}</view>
              <view class="tags" v-if="item.videoType">
                <span>{{ item.videoType }}</span>
              </view>
            </view>
          </view>
        </view>
        <view class="xiaomovie-list" v-show="activeTab === 'xiaomovie'">
          <!-- 加载中提示 -->
          <view class="loading-container" v-if="loading">
            <view class="loading-spinner"></view>
            <text class="loading-text">正在加载历史记录...</text>
          </view>

          <!-- 无数据提示 -->
          <view class="empty-container" v-else-if="xiaomovieHistory.length === 0">
            <view class="empty-icon">🎥</view>
            <text class="empty-text">暂无小视频观看历史</text>
          </view>

          <!-- 历史记录列表 -->
          <view class="movie-card" v-else v-for="(item, index) in xiaomovieHistory" :key="index" @click="navigateToVideo(item)">
            <view class="cover">
              <image :src="item.videoPic || 'https://placehold.co/300x400'" mode="aspectFill"/>
            </view>
            <view class="info">
              <h3 class="movie-title">{{ item.videoName }}</h3>
              <view class="fabu">{{ formatDate(item.timestamp) }}</view>
              <view class="zhuyan" v-if="item.currentEpisode">当前集数：{{ item.currentEpisode }}</view>
            </view>
          </view>
        </view>
      </view>

    </view>

  </view>
</template>

<script>
import logger from '@/utils/logger';

export default {
  data() {
    return {
      activeTab: 'movie',
      loading: false,
      error: null,
      movieHistory: [], // 剧集历史
      xiaomovieHistory: [], // 小视频历史
      meituHistory: [] // 美图历史（预留）
    };
  },

  async onLoad() {
    // 加载播放历史
    await this.loadData();
  },

  methods: {
    // 切换标签
    switchTab(tab) {
      this.activeTab = tab;
    },

    // 加载数据
    async loadData() {
      try {
        this.loading = true;

        // 只加载播放历史，不需要导航数据
        await this.loadPlayHistory();

      } catch (error) {
        logger.error('加载历史数据失败:', error);
        this.error = error.message || '加载数据失败';

        uni.showToast({
          title: '加载历史数据失败',
          icon: 'none',
          duration: 2000
        });
      } finally {
        this.loading = false;
      }
    },

    // 加载播放历史
    loadPlayHistory() {
      try {
        // 从本地存储获取播放历史
        let history = uni.getStorageSync('playHistory');
        if (!history) {
          this.movieHistory = [];
          this.xiaomovieHistory = [];
          return;
        }

        // 解析JSON
        if (typeof history === 'string') {
          history = JSON.parse(history);
        }

        // 根据导航数据的extraParams分类
        this.classifyHistory(history);
      } catch (error) {
        logger.error('加载播放历史失败:', error);
        this.movieHistory = [];
        this.xiaomovieHistory = [];
      }
    },

    // 分类历史记录
    classifyHistory(history) {
      logger.info('开始分类历史记录，总数:', history.length);

      // 重置分类
      this.movieHistory = [];
      this.xiaomovieHistory = [];

      // 遍历历史记录
      history.forEach((item, index) => {
        logger.info(`处理第${index+1}条历史记录:`, item.videoName);

        // 判断是否为小视频
        let isShortVideo = false;

        // 1. 先检查extraParams
        if (item.extraParams) {
          try {
            const extraParamsObj = JSON.parse(item.extraParams);
            isShortVideo = extraParamsObj && extraParamsObj.isHorizontalLayout === true;
            logger.info('基于extraParams判断:', isShortVideo ? '是小视频' : '不是小视频');
          } catch (error) {
            logger.error('解析extraParams失败:', error);
            // 兼容旧版本
            isShortVideo = item.extraParams === '0';
          }
        }
        // 2. 如果视频类型是“短剧”，直接判断为小视频
        else if (item.videoType === '短剧') {
          isShortVideo = true;
          logger.info('基于视频类型“短剧”判断为小视频');
        }

        // 根据判断结果分类
        if (isShortVideo) {
          // 小视频放入xiaomovieHistory
          this.xiaomovieHistory.push(item);
        } else {
          // 其他的放入剧集
          this.movieHistory.push(item);
        }
      });

      logger.info('分类完成，小视频数量:', this.xiaomovieHistory.length, '剧集数量:', this.movieHistory.length);
    },

    // 导航到视频详情页
    navigateToVideo(item) {
      if (!item || !item.id) return;

      uni.navigateTo({
        url: `/pages/view/view?id=${item.id}`
      });
    },

    // 格式化时间戳
    formatDate(timestamp) {
      if (!timestamp) return '';

      const date = new Date(timestamp);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
    }
  }
};
</script>


<style scoped lang="scss">

.subnav {
  ul {
    display: flex;
    align-items: center;
    height: 60rpx;
    color: #6f6f71;
    font-size: 36rpx;
    margin: 0 40rpx 30rpx;
    justify-content: space-evenly;

    li{
      height: 68rpx;
    }

    li.active {
      color: #fff;
      font-weight: 600;
      position: relative;
    }
    li.active:before{
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%) scaleX(1);
      transform-origin: center;
      width: 40rpx;
      height: 6rpx;
      background: linear-gradient(315deg, #fe748c, #fe748e);
      border-radius: 3rpx;
      transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1), background-color 0.3s ease;
      color: #e3e3e5;
    }
  }
}

// 加载中状态样式
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300rpx;
  width: 100%;

  .loading-spinner {
    width: 80rpx;
    height: 80rpx;
    border: 6rpx solid #f3f3f3;
    border-top: 6rpx solid #e62c17;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  .loading-text {
    margin-top: 20rpx;
    font-size: 28rpx;
    color: #666;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 空数据状态样式
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300rpx;
  width: 100%;

  .empty-icon {
    font-size: 60rpx;
    margin-bottom: 20rpx;
  }

  .empty-text {
    font-size: 28rpx;
    color: #666;
  }
}


.movie-list {
  display: flex;
  flex-direction: column;
  gap: 40rpx;

  .movie-card {
    display: flex;
    gap: 20rpx;

    .info {
      width: 65%;
    }

    .cover {
      width: 30vw;
      flex-shrink: 0;
      position: relative;
      height: auto;

      .rank-badge {
        position: absolute;
        top: 0;
        width: 36rpx;
        height: 36rpx;
        background: rgba(0, 0, 0, .7);
        border-radius: 6rpx;
        font-size: 28rpx;
        font-weight: 600;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      image {
        width: 100%;
        border-radius: 6rpx;
        display: block;
      }
    }

    .fabu, .zhuyan, .tags {
      font-size: 22rpx;
      color: #969699;
      padding: 0 20rpx 0 0;
      border-radius: 4rpx;
      line-height: 50rpx;

      span {
        padding: 0 20rpx 0 0;
      }
    }

    .movie-title {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      margin-bottom: 20rpx;
    }
  }
}


.xiaomovie-list {
  display: flex;
  flex-direction: column;
  gap: 40rpx;

  .movie-card {
    display: flex;
    gap: 20rpx;

    .info {
      width: 65%;
    }

    .cover {
      width: 30vw;
      flex-shrink: 0;
      position: relative;
      height: auto;

      .rank-badge {
        position: absolute;
        top: 0;
        width: 36rpx;
        height: 36rpx;
        background: rgba(0, 0, 0, .7);
        border-radius: 6rpx;
        font-size: 28rpx;
        font-weight: 600;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      image {
        width: 100%;
        border-radius: 6rpx;
        display: block;
      }
    }

    .fabu, .zhuyan, .tags {
      font-size: 22rpx;
      color: #969699;
      padding: 0 20rpx 0 0;
      border-radius: 4rpx;
      line-height: 50rpx;

      span {
        padding: 0 20rpx 0 0;
      }
    }

    .movie-title {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      margin-bottom: 20rpx;
    }
  }
}


</style>
