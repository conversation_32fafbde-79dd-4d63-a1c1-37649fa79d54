<template>
  <view class="container hot">
    <!-- 背景图层，添加transform控制向上移动 -->
    <view class="bg-layer" :style="{
      opacity: backgroundOpacity,
      transform: `translateY(${backgroundOffset}px)`
    }"></view>

    <view class="content-area">
      <!-- 加载状态 -->
      <view v-if="loading && !currentItems.length" class="loading-container">
        <view class="loading-spinner"></view>
        <text class="loading-text">正在加载排行榜数据...</text>
      </view>

      <!-- 错误提示 -->
      <view v-if="error && !currentItems.length" class="error-container">
        <text class="error-text">{{ error }}</text>
        <view class="error-retry" @click="loadRankingData">点击重试</view>
      </view>
      <!-- 主导航 -->
      <view class="tabs">
        <view
            v-for="(category, index) in categories"
            :key="index"
            class="tabs-item"
            :class="{ active: currentCategory === index }"
            @click="changeCategory(index)"
        >
          {{ category.name }}
        </view>
      </view>

      <!-- 子导航 -->
      <view class="sub-nav">
        <view
            v-for="(period, index) in periods"
            :key="index"
            class="sub-nav-item"
            :class="{ active: currentPeriod === index }"
            @click="changePeriod(index)"
        >
          {{ period.name }}
        </view>
      </view>

      <!-- 内容区域 -->
      <!-- 前三名横向布局 -->
      <view class="movie-list top-three">
          <view
              v-for="(item, index) in currentItems.slice(0, 3)"
              :key="index"
              class="movie-card"
          >
            <navigator :url="`/pages/view/view?id=${item.videoId}`">
            <view class="cover">
              <image :src="item.poster" :alt="item.title" mode="aspectFill" />
              <view class="rank-badge" :class="`rank-${index+1}`">{{ index + 1 }}</view>
            </view>
            </navigator>
            <view class="info">
              <navigator :url="`/pages/view/view?id=${item.videoId}`">
              <h3 class="movie-title">{{ item.title }}</h3>
              </navigator>
              <view class="movie-info">{{ item.year }}{{ item.extra ? '/' + item.extra : '' }}</view>
              <view class="movie-tags">
                <span
                    v-for="(tag, tagIndex) in item.tags"
                    :key="tagIndex"
                    class="movie-tag"
                >
                  {{ tag }}
                </span>
              </view>
            </view>
          </view>
      </view>

      <!-- 原有的电影列表，只显示第4个及以后的内容 -->
      <view class="movie-list">
        <view
            v-for="(item, index) in currentItems.slice(3)"
            :key="index + 3"
            class="movie-card"
        >
          <navigator :url="`/pages/view/view?id=${item.videoId}`">
          <view class="cover">
            <image :src="item.poster" :alt="item.title" mode="aspectFill" />
            <view class="rank-badge">{{ index + 4 }}</view>
          </view>
          </navigator>
          <view class="info">
            <navigator :url="`/pages/view/view?id=${item.videoId}`">
              <h3 class="movie-title">{{ item.title }}</h3>
            </navigator>
            <view class="movie-info">{{ item.year }}{{ item.extra ? '/' + item.extra : '' }}</view>
            <view class="movie-tags">
          <span
              v-for="(tag, tagIndex) in item.tags"
              :key="tagIndex"
              class="movie-tag">
            {{ tag }}
          </span>
            </view>
          </view>
        </view>
      </view>
    </view>

  </view>
</template>

<script>
import { getVideoRankings, getVideoRankingByType, getVideoTypeList, parseRankingData, RankingType, TypeDisplayType } from '../../api/videoRanking';
import { getCachedNavigationList } from '../../api/navigationCache';
import cacheManager from '../../utils/cacheManager';
import logger from '../../utils/logger';

export default {
  name: 'hot',
  data() {
    return {
      currentCategory: 0, // 默认选中电影
      currentPeriod: 0,   // 默认选中周榜
      backgroundOpacity: 1, // 背景透明度，初始为1（完全可见）
      backgroundOffset: 0, // 背景偏移量，控制向上移动
      loading: false, // 加载状态
      error: null, // 错误信息
      categories: [
        // 初始化为空数组，将在 loadAllRankings 中从导航 API 加载竖屏分类
      ],
      periods: [
        {name: '周榜', key: 'weekly', type: RankingType.Week},
        {name: '月榜', key: 'monthly', type: RankingType.Month},
        {name: '总榜', key: 'total', type: RankingType.Total}
      ],
      // 默认数据，用于加载失败时显示
      fallbackData: {
        title: '加载中...',
        year: '',
        tags: [],
        poster: 'https://placehold.co/300x400',
        videoId: ''
      }
    };
  },
  computed: {
    currentItems() {
      // 检查分类数据是否存在
      if (!this.categories || this.categories.length === 0) {
        return [];
      }

      const category = this.categories[this.currentCategory];
      if (!category || !category.content) {
        return [];
      }

      const periodKey = this.periods[this.currentPeriod].key;
      return category.content[periodKey] || [];
    }
  },
  methods: {
    // 切换分类
    async changeCategory(index) {
      console.log(`切换分类: 从 ${this.currentCategory} 到 ${index}`);

      // 即使是同一个分类，也强制重新加载
      this.currentCategory = index;
      await this.loadRankingData();
    },

    // 切换周期
    async changePeriod(index) {
      console.log(`切换周期: 从 ${this.currentPeriod} 到 ${index}`);

      // 即使是同一个周期，也强制重新加载
      this.currentPeriod = index;
      await this.loadRankingData();
    },

    // 加载排行榜数据
    async loadRankingData() {
      try {
        this.loading = true;
        this.error = null;

        const category = this.categories[this.currentCategory];
        const period = this.periods[this.currentPeriod];

        console.log(`加载排行榜数据: 类型=${category.type}, 周期=${period.key}, 周期名=${period.name}, 类型值=${period.type}`);

        // 直接从API获取数据
        console.log(`发起API请求: videoType=${category.type}, rankingType=${period.type} (对应周期: ${period.name})`);
        const response = await getVideoRankingByType({
          videoType: category.type,
          rankingType: period.type
        });

        console.log('获取到排行榜数据:', response);
        console.log(`排行榜数据长度: ${response ? (Array.isArray(response) ? response.length : (response.items ? response.items.length : 0)) : 0}`);

        // 解析数据
        const rankingData = parseRankingData(response);

        // 更新数据
        if (rankingData && rankingData.length > 0) {
          this.categories[this.currentCategory].content[period.key] = rankingData;
        } else {
          // 如果没有数据，使用默认数据
          this.categories[this.currentCategory].content[period.key] = Array(5).fill().map(() => ({ ...this.fallbackData }));
        }
      } catch (error) {
        console.error('加载排行榜数据失败:', error);
        this.error = error.message || '加载数据失败，请稍后重试';

        // 显示错误提示
        uni.showToast({
          title: this.error,
          icon: 'none',
          duration: 2000
        });

        // 使用默认数据
        this.categories[this.currentCategory].content[this.periods[this.currentPeriod].key] = Array(5).fill().map(() => ({ ...this.fallbackData }));
      } finally {
        this.loading = false;
      }
    },

    // 加载所有分类的排行榜数据
    async loadAllRankings() {
      try {
        this.loading = true;

        // 使用导航 API 获取分类数据
        const navigationResponse = await getCachedNavigationList();
        logger.info('获取到导航数据:', navigationResponse);

        if (navigationResponse && Array.isArray(navigationResponse)) {
          // 过滤出竖屏内容（排除横屏内容）
          const verticalCategories = navigationResponse
            .filter(nav => {
              // 只显示移动端的导航项，兼容大小写字段名
              const isH5Display = nav.IsH5Display || nav.isH5Display;
              if (!isH5Display) return false;

              // 解析 extraParams，兼容大小写字段名
              const extraParams = nav.ExtraParams || nav.extraParams;
              let extraParamsObj = null;
              try {
                if (extraParams && typeof extraParams === 'string') {
                  extraParamsObj = JSON.parse(extraParams);
                }
              } catch (error) {
                logger.error('解析extraParams失败:', error, extraParams);
                // 如果解析失败，认为是竖屏内容
                return true;
              }

              // 排除 isHorizontalLayout 为 true 的横屏内容
              return !(extraParamsObj && extraParamsObj.isHorizontalLayout === true);
            })
            .map(nav => {
              const categotyName = nav.CategotyName || nav.categotyName;
              return {
                name: categotyName,
                type: categotyName,
                content: { weekly: [], monthly: [], total: [] }
              };
            });

          logger.info('过滤后的竖屏分类:', verticalCategories);

          // 更新分类数据
          if (verticalCategories.length > 0) {
            this.categories = verticalCategories;
          } else {
            // 如果没有符合条件的分类，使用默认分类
            logger.warn('没有找到竖屏分类，使用默认分类');
            this.categories = [
              { name: '电影', type: '电影', content: { weekly: [], monthly: [], total: [] } },
              { name: '电视剧', type: '电视剧', content: { weekly: [], monthly: [], total: [] } },
              { name: '综艺', type: '综艺', content: { weekly: [], monthly: [], total: [] } },
              { name: '动漫', type: '动漫', content: { weekly: [], monthly: [], total: [] } }
            ];
          }
        } else {
          // 如果导航 API 失败，使用默认分类
          logger.warn('导航 API 返回数据为空，使用默认分类');
          this.categories = [
            { name: '电影', type: '电影', content: { weekly: [], monthly: [], total: [] } },
            { name: '电视剧', type: '电视剧', content: { weekly: [], monthly: [], total: [] } },
            { name: '综艺', type: '综艺', content: { weekly: [], monthly: [], total: [] } },
            { name: '动漫', type: '动漫', content: { weekly: [], monthly: [], total: [] } }
          ];
        }

        // 确保当前选中的分类索引在有效范围内
        if (this.currentCategory >= this.categories.length) {
          this.currentCategory = 0;
        }

        logger.info('最终分类数据:', this.categories);

        // 加载当前选中的排行榜数据
        await this.loadRankingData();
      } catch (error) {
        logger.error('加载分类数据失败:', error);
        this.error = error.message || '加载数据失败，请稍后重试';

        // 使用默认分类作为备选
        this.categories = [
          { name: '电影', type: '电影', content: { weekly: [], monthly: [], total: [] } },
          { name: '电视剧', type: '电视剧', content: { weekly: [], monthly: [], total: [] } },
          { name: '综艺', type: '综艺', content: { weekly: [], monthly: [], total: [] } },
          { name: '动漫', type: '动漫', content: { weekly: [], monthly: [], total: [] } }
        ];

        // 显示错误提示
        uni.showToast({
          title: this.error,
          icon: 'none',
          duration: 2000
        });
      } finally {
        this.loading = false;
      }
    },

    // 跳转到视频详情页
    goToVideoDetail(videoId) {
      if (!videoId) return;

      uni.navigateTo({
        url: `/pages/view/view?id=${videoId}`
      });
    }
  },
  onPageScroll(e) {
    // 获取滚动距离
    const scrollTop = e.scrollTop;
    // 设定透明度渐变阈值
    const fadeThreshold = 200;
    // 计算透明度: 1 -> 0 渐变
    let opacity = 1 - (scrollTop / fadeThreshold);
    // 限制透明度范围在 0-1 之间
    opacity = Math.max(0, Math.min(1, opacity));
    // 计算背景向上偏移量
    // 滚动100px时，背景上移-30px
    const moveRatio = -0.6;
    const bgOffset = scrollTop * moveRatio;
    // 更新背景透明度和位置
    this.backgroundOpacity = opacity;
    this.backgroundOffset = bgOffset;
  },
  async onLoad() {
    // 确保初始加载时背景可见
    this.backgroundOpacity = 1;

    // 加载排行榜数据
    await this.loadAllRankings();

    // 确保当前选中的排行榜数据已加载
    console.log('加载当前选中的排行榜数据');
    await this.loadRankingData();
  },
  onPullDownRefresh() {
    // 下拉刷新，强制重新加载数据
    this.loadRankingData().then(() => {
      uni.stopPullDownRefresh();
    });
  }
};
</script>

<style scoped lang="scss">
.hot {
  height: 100vh;
  width: 100%;
  position: relative;
}

.bg-layer {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100vh;
  background: url(../../static/rb_bg.png) 0 0 no-repeat;
  background-size: 100% auto;
  z-index: 1;
  transition: opacity 0.2s ease, transform 0.2s ease;
}

.hot .content-area {
  background-color: #101012;
  padding: 0 20rpx;
  padding-bottom: 100rpx;
  margin-top: 35vw;
  position: relative;
  z-index: 2;
  border-top-left-radius: 20rpx;
  border-top-right-radius: 20rpx;
}

.tabs {
  display: flex;
  height: 110rpx;
  align-items: center;
  justify-content: space-around;
  font-size: 36rpx;
  color: #c8c8cc;
}

.tabs-item {
  position: relative;
  height: 68rpx;
}

.tabs .active:before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%) scaleX(1);
  transform-origin: center;
  width: 40rpx;
  height: 6rpx;
  background: linear-gradient(315deg, #fe748c, #fe748e);
  border-radius: 3rpx;
  transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1), background-color 0.3s ease;
  color: #e3e3e5;
}

.sub-nav {
  display: flex;
  justify-content: space-evenly;
  font-size: 28rpx;
  color: #969699;
  height: 88rpx;
  align-items: center;
}

.sub-nav .active {
  font-size: 36rpx;
  font-weight: 600;
  color: #ff743d;
}

.movie-list {
  display: flex;
  flex-direction: column;
  gap: 40rpx;

  .movie-card {
    display: flex;
    gap: 20rpx;

    .info{
      width: auto;
      overflow: hidden;
    }

    .cover {
      width: 30vw;
      height: 42vw;
      flex-shrink: 0;
      position: relative;

      .rank-badge {
        position: absolute;
        top: 0;
        width: 36rpx;
        height: 36rpx;
        background: rgba(0, 0, 0, .7);
        border-radius: 6rpx;
        font-size: 28rpx;
        font-weight: 600;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      image {
        width: 100%;
        border-radius: 6rpx;
        display: block;
        height: 100%;
      }
    }

    .movie-tag,.movie-info{
      font-size: 22rpx;
      color: #969699;
      padding: 0 20rpx 0 0;
      border-radius: 4rpx;
      line-height: 50rpx;
    }
    .movie-title{
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      margin-bottom: 20rpx;
    }
  }
}

.top-three{
  margin-bottom: 40rpx;

  .movie-tag,.movie-info{
    line-height: 40rpx!important;
  }

}

.movie-list.top-three {
  margin: 20rpx 0 40rpx 0;
  display: flex;
  gap: 20rpx;
  flex-direction: row;

  .movie-card{
    display: flex;
    flex-direction: column;
    width: calc(33.334vw - 26.667rpx);
  }
}


.top-cover image {
  width: 100%;
  border-radius: 6rpx;
  display: block;
}


.rank-1 {
  background: linear-gradient(180deg, #FF009E 0%, #FF0066 100%)!important;
}

.rank-2 {
  background: linear-gradient(180deg, #FFAB6F 0%, #FF743D 100%)!important;;
}

.rank-3 {
  background: linear-gradient(180deg, #FBE2D2 0%, #F7C3A7 100%)!important;;
  color: #964E27;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid rgba(255, 116, 61, 0.2);
  border-top-color: #ff743d;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #969699;
}

/* 错误提示样式 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
}

.error-text {
  font-size: 28rpx;
  color: #969699;
  text-align: center;
  margin-bottom: 20rpx;
}

.error-retry {
  padding: 10rpx 30rpx;
  background: rgba(255, 116, 61, 0.1);
  border: 1px solid #ff743d;
  border-radius: 30rpx;
  color: #ff743d;
  font-size: 24rpx;
}

</style>
