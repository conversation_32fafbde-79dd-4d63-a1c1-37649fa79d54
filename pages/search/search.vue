<template>
  <view class="container history">
    <view class="content-area">
      <view class="search">
        <view class="fanhui" @click="navigateTo('/')"><i class="icon fanhui"></i></view>
        <view class="input">
          <input type="text" v-model="searchText" @input="handleInput" @keyup.enter="handleSearch">
          <i class="icon qingkong" v-show="searchText.length > 0" @click="clearSearch"></i>
        </view>
        <view class="button" @click="handleSearch">搜索</view>
      </view>

      <!-- 搜索提示，仅在有搜索内容时显示 -->
<!--      <view class="sousuotishi" v-show="searchText.length > 0">-->
<!--        <ul>-->
<!--          <li>知否知否应否知否应否知否应是绿肥红瘦</li>-->
<!--          <li>知否知否应否知否应否知否应是绿肥红瘦</li>-->
<!--          <li>知否知否应否知否应否知否应是绿肥红瘦</li>-->
<!--          <li>知否知否应否知否应否知否应是绿肥红瘦</li>-->
<!--          <li>知否知否应否知否应否知否应是绿肥红瘦</li>-->
<!--          <li>知否知否应否知否应否知否应是绿肥红瘦</li>-->
<!--          <li>知否知否应否知否应否知否应是绿肥红瘦</li>-->
<!--          <li>知否知否应否知否应否知否应是绿肥红瘦</li>-->
<!--          <li>知否知否应否知否应否知否应是绿肥红瘦</li>-->
<!--          <li>知否知否应否知否应否知否应是绿肥红瘦</li>-->
<!--        </ul>-->
<!--      </view>-->

      <!-- 历史搜索和热搜区域 -->
      <view class="initial" v-if="!showSearchResults">
        <view class="history">
          <view class="title">历史搜索 <span @click="toggleHistory">{{ isExpanded ? '收起' : '展开' }}</span></view>
          <view class="loading" v-if="loadingHistory">
            <i class="icon loading"></i> 加载中...
          </view>
          <view v-else-if="historyList.length === 0" class="no-history">
            暂无历史搜索记录
          </view>
          <ul class="sousuolist" v-else>
            <li v-for="(item, index) in displayedHistory" :key="index" @click="searchByKeyword(item)">
              {{ item }}
            </li>
          </ul>
        </view>
        <view class="clear-log" v-if="historyList.length > 0" @click="clearSearchHistory"><i class="icon shanchu"></i>清空搜索历史</view>

        <view class="history resou">
          <view class="title">热搜</view>
          <view class="loading" v-if="loadingHotSearch">
            <i class="icon loading"></i> 加载中...
          </view>
          <ul class="sousuolist" v-else>
            <li v-for="(item, index) in hotSearchList" :key="index" @click="searchByKeyword(item.keyword)">
              <span>{{ index + 1 }}</span>{{ item.keyword }}
            </li>
          </ul>
        </view>
      </view>

      <!-- 搜索结果区域 -->
      <view class="search-results" v-if="showSearchResults">
        <!-- 搜索中提示 -->
        <view class="loading-container" v-if="isSearching">
          <view class="loading-spinner"></view>
          <text class="loading-text">正在搜索...</text>
        </view>

        <!-- 搜索结果信息 -->
        <view class="search-info" v-else>
          <view class="search-keyword">
            搜索“<span>{{ searchText }}</span>”，找到 {{ totalCount }} 条结果
          </view>
        </view>

        <!-- 搜索结果列表 -->
        <view class="box" v-if="searchResults.length > 0">
          <view class="title">搜索结果</view>
          <ul class="tuijianlist">
            <li v-for="(item, index) in searchResults" :key="index">
              <view class="cover">
                <view @click="handleNavigateToVideo(item.id)">
                  <image :src="(item.VideoPic || item.videoPic) || 'https://placehold.co/500x283'" mode="widthFix"/>
                  <view class="video-info">
                    <view class="pingfen" v-if="item.VideoScore || item.videoScore">{{ item.VideoScore || item.videoScore }}</view>
                    <view class="renqitime">
                      <span>{{ (item.VideoHits || item.videoHits) || 0 }}</span>
                      <span>{{ (item.VideoDuration || item.videoDuration) || (item.VideoRemarks || item.videoRemarks) || '' }}</span>
                    </view>
                  </view>
                </view>
              </view>
              <view class="con">
                <view @click="handleNavigateToVideo(item.id)">
                  <view class="biaoti">{{ item.VideoName || item.videoName }}</view>
                </view>
                <view class="bottom">
                  <view class="tags" v-if="item.VideoType || item.videoType">
                    {{ item.VideoType || item.videoType }}
                  </view>
                  <view class="zhuyan" v-if="item.VideoActor || item.videoActor">主演：{{ item.VideoActor || item.videoActor }}</view>
                  <view class="time" v-if="item.VideoAddTime || item.videoAddTime">{{ formatDate(item.VideoAddTime || item.videoAddTime) }}</view>
                </view>
              </view>
            </li>
          </ul>

          <!-- 加载状态提示 -->
          <view class="load-more">
            <view v-if="loadingMore" class="loading">加载中...</view>
            <view v-else-if="!hasMore" class="no-more">没有更多数据了</view>
          </view>
        </view>

        <!-- 无搜索结果提示 -->
        <view class="no-results" v-else-if="!isSearching">
          <image src="/static/nofind.png" mode="widthFix"/>
          <view class="text">没有找到相关内容</view>
          <view class="tips">换个关键词试试吧！</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getHotSearchRecords, searchHistory, searchVideos } from '@/api/search';
import VideoView from "@/pages/view/view.vue";
import logger from '@/utils/logger';

export default {
  name: 'search',
  components: {VideoView},
  data() {
    return {
      searchText: '', // 搜索框内容
      isExpanded: false,
      historyList: [], // 历史搜索列表
      hotSearchList: [], // 热搜列表
      loadingHotSearch: false, // 是否正在加载热搜
      loadingHistory: false, // 是否正在加载历史搜索

      // 搜索结果相关
      searchResults: [], // 搜索结果列表
      totalCount: 0, // 搜索结果总数
      isSearching: false, // 是否正在搜索
      showSearchResults: false, // 是否显示搜索结果
      currentPage: 1, // 当前页码
      pageSize: 10, // 每页显示数量
      hasMore: true, // 是否还有更多数据
      loadingMore: false // 是否正在加载更多
    }
  },
  computed: {
    displayedHistory() {
      return this.isExpanded
          ? this.historyList
          : this.historyList.slice(0, 10);
    }
  },
  mounted() {
    // 页面加载时获取热搜数据和历史搜索
    this.loadHotSearch();
    this.loadHistorySearch();
  },
  methods: {

    navigateTo(path) {
      // 使用编程式导航跳转
      this.$router.push(path);
    },

    // 处理输入框内容变化
    handleInput() {
      // 可以在这里添加其他逻辑，如获取搜索提示等
    },

    // 清空搜索内容
    clearSearch() {
      this.searchText = '';
    },

    // 点击搜索按钮或回车搜索
    async handleSearch() {
      const keyword = this.searchText.trim();
      if (!keyword) return;

      // 添加到历史搜索
      searchHistory.addHistory(keyword);
      this.loadHistorySearch(); // 刷新历史列表

      // 执行搜索
      this.currentPage = 1; // 重置页码
      await this.performSearch(keyword);


    },

    // 执行搜索
    async performSearch(keyword, isLoadMore = false) {
      if (!keyword) return;

      // 如果是加载更多，设置加载更多状态
      if (isLoadMore) {
        if (this.loadingMore || !this.hasMore) return;
        this.loadingMore = true;
      } else {
        this.isSearching = true;
        this.showSearchResults = true;
      }

      try {
        const response = await searchVideos({
          keyword: keyword,
          page: this.currentPage,
          pageSize: this.pageSize
        });

        logger.info('搜索结果:', response);

        // 处理不同的响应格式
        let newResults = [];

        if (response && response.items && Array.isArray(response.items)) {
          newResults = response.items;
          this.totalCount = response.totalCount || 0;
        } else if (response && response.data && Array.isArray(response.data)) {
          newResults = response.data;
          this.totalCount = response.totalCount || 0;
        } else if (Array.isArray(response)) {
          newResults = response;
          this.totalCount = response.length;
        }

        // 判断是否还有更多数据
        this.hasMore = newResults.length === this.pageSize;

        // 更新搜索结果
        if (isLoadMore) {
          // 合并数据
          this.searchResults = [...this.searchResults, ...newResults];
          // 更新页码
          if (newResults.length > 0) {
            this.currentPage++;
          }
        } else {
          // 替换数据
          this.searchResults = newResults;
        }
      } catch (error) {
        logger.error('搜索失败:', error);

        if (!isLoadMore) {
          this.searchResults = [];
          this.totalCount = 0;
        }

        this.hasMore = false;

        // 显示错误提示
        uni.showToast({
          title: '搜索失败，请重试',
          icon: 'none',
          duration: 2000
        });
      } finally {
        if (isLoadMore) {
          this.loadingMore = false;
        } else {
          this.isSearching = false;
        }
      }
    },

    // 切换历史记录展开/收起
    toggleHistory() {
      this.isExpanded = !this.isExpanded;
    },

    // 加载热搜数据
    async loadHotSearch() {
      this.loadingHotSearch = true;
      try {
        const response = await getHotSearchRecords();
        logger.info('热搜数据:', response);

        // 处理不同的响应格式
        if (response && response.data && Array.isArray(response.data)) {
          this.hotSearchList = response.data;
        } else if (Array.isArray(response)) {
          this.hotSearchList = response;
        } else {
          this.hotSearchList = [];
        }

        // 限制最多显示10条
        if (this.hotSearchList.length > 10) {
          this.hotSearchList = this.hotSearchList.slice(0, 10);
        }
      } catch (error) {
        logger.error('加载热搜失败:', error);
        this.hotSearchList = [];
      } finally {
        this.loadingHotSearch = false;
      }
    },

    // 点击热搜项或历史搜索项进行搜索
    async searchByKeyword(keyword) {
      if (!keyword) return;

      // 添加到历史搜索
      searchHistory.addHistory(keyword);
      this.loadHistorySearch(); // 刷新历史列表

      // 设置搜索框内容
      this.searchText = keyword;

      // 执行搜索
      this.currentPage = 1; // 重置页码
      await this.performSearch(keyword);


    },

    // 加载历史搜索
    loadHistorySearch() {
      this.loadingHistory = true;
      try {
        // 从本地存储获取历史搜索
        this.historyList = searchHistory.getHistory();

      } catch (error) {
        // 加载失败时设置为空数组
        this.historyList = [];
      } finally {
        this.loadingHistory = false;
      }
    },

    // 清空历史搜索
    clearSearchHistory() {
      try {
        searchHistory.clearHistory();
        this.historyList = [];
        logger.info('历史搜索已清空');
      } catch (error) {
        logger.error('清空历史搜索失败:', error);
      }
    },

    // 返回上一页
    goBack() {
      // 如果当前显示的是搜索结果，则返回到初始状态
      if (this.showSearchResults) {
        this.showSearchResults = false;
        this.searchText = '';
        return;
      }

      // 否则返回上一页
      uni.navigateBack({
        delta: 1,
        fail: function() {
          // 如果没有上一页，则跳转到首页
          uni.switchTab({
            url: '/pages/index/index'
          });
        }
      });
    },

    // 跳转到视频详情页
    handleNavigateToVideo(videoId) {
      if (!videoId) return;

      uni.navigateTo({
        url: `/pages/view/view?id=${videoId}`
      });
    },

    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return '';

      try {
        const date = new Date(dateString);
        return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
      } catch (error) {
        logger.error('日期格式化失败:', error);
        return dateString;
      }
    },

    // 加载更多数据
    loadMore() {
      if (this.hasMore && !this.loadingMore && this.searchText) {
        this.performSearch(this.searchText, true);
      }
    }
  },

  // 页面触底事件
  onReachBottom() {
    logger.info('页面触底，加载更多数据');
    this.loadMore();
  }
}
</script>


<style scoped lang="scss">
/* 搜索结果区域样式 */
.search-results {
  margin-top: 20rpx;
}

.search-info {
  padding: 20rpx 0;
}

.search-keyword {
  font-size: 28rpx;
  color: #969699;
  span{
    color: #fe748c;
    padding: 0 5px;
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.1);
  border-top: 4rpx solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #969699;
}

.box {
  margin-top: 20rpx;

  .title {
    font-size: 32rpx;
    font-weight: bold;
    margin-bottom: 20rpx;
  }

  .tuijianlist {
    li {
      display: flex;
      margin-bottom: 30rpx;

      .cover {
        width: 240rpx;
        position: relative;
        border-radius: 10rpx;
        overflow: hidden;
        flex: none;
        height: auto;

        image {
          width: 100%;
          height: auto;
        }

        .video-info {
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          display: flex;
          justify-content: space-between;
          padding: 10rpx;
          background: linear-gradient(to top, rgba(0,0,0,0.7), transparent);

          .pingfen {
            color: #ff6600;
            font-size: 24rpx;
          }

          .renqitime {
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 13px;

            span:first-child {
              display: flex;
              align-items: center;
            }

            span:first-child:before {
              content: "";
              background: url("/static/renqi.png") left center no-repeat;
              width: 24rpx;
              height: 24rpx;
              background-size: 100%;
              display: block;
              margin-right: 6rpx;
            }
          }
        }
      }

      .con {
        flex: 1;
        padding-left: 20rpx;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .biaoti {
          font-size: 28rpx;
          line-height: 1.4;
          margin-bottom: 10rpx;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
        }

        .bottom {
          font-size: 24rpx;
          color: #969699;

          uni-view{
            margin-top: 10px;
          }

          .zhuyan {
            margin-bottom: 10rpx;
            overflow: hidden;
            text-overflow: ellipsis;
            font-size: 13px;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            text-overflow: ellipsis;
          }
        }
      }
    }
  }
}

.no-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;

  image {
    width: 200rpx;
    height: auto;
    margin-bottom: 30rpx;
  }

  .text {
    font-size: 32rpx;
    color: #fff;
    margin-bottom: 10rpx;
  }

  .tips {
    font-size: 28rpx;
    color: #969699;
  }
}

.load-more {
  text-align: center;
  padding: 30rpx 0;
  color: #969699;
  font-size: 28rpx;

  .loading {
    display: flex;
    align-items: center;
    justify-content: center;

    &:before {
      content: '';
      width: 30rpx;
      height: 30rpx;
      margin-right: 10rpx;
      border: 2rpx solid rgba(255, 255, 255, 0.1);
      border-top: 2rpx solid #fff;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
  }

  .no-more {
    color: #6f6f71;
  }
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #969699;
  font-size: 28rpx;
  height: 100rpx;

  .icon.loading:before {
    width: 32rpx;
    height: 32rpx;
    margin-right: 10rpx;
    animation: spin 1s linear infinite;
  }
}

.no-history {
  color: #969699;
  font-size: 28rpx;
  text-align: center;
  padding: 30rpx 0;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.search {
  display: flex;
  gap: 10rpx;
  align-items: center;

  .fanhui:before {
    width: 48rpx;
    height: 48rpx;
    margin: 0;
  }

  .input {
    flex: 1;
    position: relative;

    .qingkong {
      position: absolute;
      top: 50%;
      right: 0;
      transform: translate(-20rpx, -50%);
    }

    .qingkong:before {
      width: 28rpx;
      height: 28rpx;
    }
  }

  input {
    background: rgb(255 255 255 / 8%);
    width: calc(100% - 60rpx);
    height: 72rpx;
    line-height: 72rpx;
    padding: 0 20rpx;
    font-size: 28rpx;
  }

  .button {
    font-size: 36rpx;
  }
}

.history {
  .title {
    font-size: 28rpx;
    display: flex;
    justify-content: space-between;

    span {
      font-size: 24rpx;
      color: #969699;
      font-weight: normal;
    }
  }

  .sousuolist {
    display: flex;
    gap: 20rpx;
    flex-wrap: wrap;

    li {
      font-size: 28rpx;
      background: #17181a;
      color: #969699;
      height: 64rpx;
      padding: 0 20rpx;
      line-height: 64rpx;
    }
  }
}

.resou {
  .sousuolist {
    column-count: 2;
    display: block;
    li{
      break-inside: avoid;
      background: none;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      color: #c8c8cc;
      align-items: center;
      padding: 0;
      line-height: 80rpx;
      height: 80rpx;
      span{
        margin-right: 10rpx;
        font-size: 28rpx;
        font-weight: 400;
        color: #6f6f71;
        text-align: center;
      }
    }
    li:first-child span{
      color: #f06;
    }
    li:nth-child(2) span{
      color: #ff743d;
    }
    li:nth-child(3) span{
      color: #f7c3a7;
    }
  }
}

.clear-log {
  color: #6f6f71;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 20px 0;

  .shanchu:before {
    width: 32rpx;
    height: 32rpx;
  }
}

.sousuotishi {
  margin-top: 30rpx;
  li {
    color: #c8c8cc;
    display: flex;
    align-items: center;
    height: 80rpx;
    line-height: 80rpx;
  }

  li:before {
    content: "";
    width: 36rpx;
    height: 36rpx;
    display: block;
    margin: 0 20rpx 0 0;
    opacity: 0.6;
    background: url("../../static/sousuo.png") left center no-repeat;
    background-size: 100%;
  }
}


</style>
