/**
 * 调试视频时长显示问题
 */

const CryptoJS = require('crypto-js');

// 正确的解密函数
function decrypt(cipherText, key = "TwksVZsnurnMwo1tWUWm47URI0vTAtzn4g3aHFddFeM=", iv = "tkGB6z24JlJcr5HxvPMYLQ==") {
  try {
    const keyWordArray = CryptoJS.enc.Base64.parse(key);
    const ivWordArray = CryptoJS.enc.Base64.parse(iv);
    const decrypted = CryptoJS.AES.decrypt(cipherText, keyWordArray, {
      iv: ivWordArray,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7
    });
    return decrypted.toString(CryptoJS.enc.Utf8);
  } catch (error) {
    console.error('解密失败:', error);
    throw error;
  }
}

// 模拟 parseVideoData 函数
function parseVideoData(data) {
  // 提取视频数组
  const videos = extractVideosArray(data);
  
  // 如果没有数据，返回空数组
  if (!videos.length) return [];
  
  // 转换每个视频项
  return videos.map(formatVideoItem);
}

function extractVideosArray(data) {
  // 如果是对象且包含 Items 属性（兼容大小写）
  if (data && typeof data === 'object' && (data.Items || data.items)) {
    return data.Items || data.items;
  }
  // 如果是数组
  if (Array.isArray(data)) {
    return data;
  }
  // 其他情况返回空数组
  return [];
}

function formatVideoItem(video) {
  // 如果视频项为空，返回空对象
  if (!video) return {};

  // 兼容大小写字段名
  const id = video.Id || video.id;
  const videoId = video.VideoId || video.videoId;
  const videoName = video.VideoName || video.videoName || video.title || '';
  const videoPic = video.VideoPic || video.videoPic || video.poster || 'https://placehold.co/300x400';
  const videoType = video.VideoType || video.videoType || '';
  const videoTag = video.VideoTag || video.videoTag || '';
  const videoScore = video.VideoScore || video.videoScore || 0;
  const videoHits = video.VideoHits || video.videoHits || 0;
  const videoDuration = video.VideoDuration || video.videoDuration || '';
  const isRecommended = video.IsRecommended || video.isRecommended || false;
  const videoAddTime = video.VideoAddTime || video.videoAddTime || '';
  const videoUpdateTime = video.VideoUpdateTime || video.videoUpdateTime || '';
  const videoActor = video.VideoActor || video.videoActor || '';
  const videoArea = video.VideoArea || video.videoArea || '';
  const videoLang = video.VideoLang || video.videoLang || '';
  const videoDirector = video.VideoDirector || video.videoDirector || '';
  const videoRemarks = video.VideoRemarks || video.videoRemarks || '';
  const videoYear = video.VideoYear || video.videoYear;

  // 返回格式化后的视频项
  return {
    id: id,
    videoId: videoId,
    title: videoName,
    poster: videoPic,
    videoName: videoName,
    videoPic: videoPic,
    videoType: videoType,
    videoTag: videoTag,
    videoScore: videoScore,
    videoHits: videoHits,
    videoDuration: videoDuration,
    isRecommended: isRecommended,
    videoAddTime: videoAddTime,
    videoUpdateTime: videoUpdateTime,
    year: videoYear,
    tags: videoTag ? videoTag.split(',') : [],
    actor: videoActor,
    area: videoArea,
    lang: videoLang,
    director: videoDirector,
    remarks: videoRemarks
  };
}

console.log('=== 调试视频时长显示问题 ===\n');

async function debugVideoDuration() {
  try {
    // 调用视频API
    const response = await fetch('https://api.player.4ii.cc/api/v1/videos', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify({
        maxResultCount: 3,
        skipCount: 0,
        sorting: 'VideoHits desc'  // 模拟热门视频排序
      })
    });

    const responseData = await response.json();
    console.log('1. API调用成功');

    if (responseData.code === 200 && responseData.data) {
      console.log('2. 开始解密数据...');
      const decryptedString = decrypt(responseData.data);
      const videoData = JSON.parse(decryptedString);

      console.log('3. 解密成功！');

      // 使用 parseVideoData 处理数据
      const parsedVideos = parseVideoData(videoData);
      
      console.log('4. 数据解析完成，视频数量:', parsedVideos.length);

      if (parsedVideos.length > 0) {
        console.log('\n=== 解析后的视频数据结构 ===');
        
        parsedVideos.slice(0, 2).forEach((video, index) => {
          console.log(`\n--- 视频 ${index + 1} ---`);
          console.log('title:', video.title);
          console.log('videoDuration:', video.videoDuration);
          console.log('remarks:', video.remarks);
          console.log('hits:', video.hits);
          console.log('score:', video.score);
          
          // 模拟模板中的显示逻辑
          const displayTime = video.videoDuration || video.remarks || '';
          console.log('模板显示结果 (videoDuration || remarks):', displayTime);
        });
      }
    } else {
      console.log('2. API返回错误或无数据');
    }

  } catch (error) {
    console.error('调试失败:', error.message);
  }
}

// 运行调试
debugVideoDuration().then(() => {
  console.log('\n=== 调试完成 ===');
});
