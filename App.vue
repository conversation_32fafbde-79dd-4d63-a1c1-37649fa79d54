<script>
import logger from './utils/logger';

export default {
	onLaunch: function() {
		logger.log('App Launch');
	},
	onShow: function() {
		logger.log('App Show');
	},
	onHide: function() {
		logger.log('App Hide');
	}
}
</script>

<style>
image{
  border-radius: 10rpx 10rpx 0 0;
}
.uni-tabbar-top, .uni-tabbar-bottom, .uni-tabbar-top .uni-tabbar, .uni-tabbar-bottom .uni-tabbar,.top-tabs {
  z-index: 999999;
}
li,ul{
  list-style: none;
  padding: 0;
  margin: 0;
}
	/*每个页面公共css */
  .container {
    display: flex;
    flex-direction: column;
    color: #e3e3e5;
    font-size: 28rpx;
  }
  uni-tabbar .uni-tabbar__label{
    font-size: 22rpx !important;
  }

  .uni-tabbar{
    height: 104rpx!important;
  }

  .uni-tabbar .uni-tabbar__icon {
    width: 48rpx !important;
    height: 48rpx !important;
  }

  /* 顶部标签样式 */
  .top-tabs {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #121212;
    padding: 0 20rpx;
    height: 100rpx;
    position: sticky;
    top: 0;
    box-sizing: border-box;
    width: 100%;
  }

  /* Logo 区域 */
  .logo-area {
    width: 70rpx;
    height: 70rpx;
    flex-shrink: 0;
    margin-right: 10rpx;
  }

  .app-logo {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }

  /* 中间标签区域 */
  .tabs-container {
    flex: 1;
    height: 100%;
    margin: 0 10rpx;
    white-space: nowrap;
    min-width: 50%;
    max-width: calc(100% - 240rpx);
  }

  /* 隐藏滚动条 */
  .tabs-container::-webkit-scrollbar {
    display: none;
  }

  .tab-item {
    display: inline-block;
    position: relative;
    padding: 0 30rpx;
    text-align: center;
    font-size: 37rpx;
    color: #c8c8cc;
    height: 100%;
    line-height: 100rpx;
  }

  .active-tab {
    color: #ffffff;
    font-weight: bold;
    font-size: 40rpx;
  }
  /* 为所有tab-item添加过渡位置样式，确保标记的正确定位和动画 */
  .tab-item::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%) scaleX(0);
    transform-origin: center;
    width: 40rpx;
    height: 6rpx;
    background-color: transparent;
    border-radius: 3rpx;
    transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1), background-color 0.3s ease;
  }
  .active-tab::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%) scaleX(1);
    transform-origin: center;
    width: 40rpx;
    height: 6rpx;
    background: linear-gradient(315deg,#fe748c,#fe748e);
    border-radius: 3rpx;
    transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1), background-color 0.3s ease;
  }

  /* 右侧图标区域 */
  .right-icons {
    display: flex;
    align-items: center;
    flex-shrink: 0;
    justify-content: flex-end;
  }
  .right-menu{
    display: flex;
  }

.right-menu li{
  display: flex;
  margin-left: 30rpx;
}

  .icon-item {
    padding: 0 10rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .icon-img {
    width: 52rpx;
    height: 52rpx;
    object-fit: contain;
  }

  /* 内容区域样式 */
  .content-area {
    flex: 1;
    padding: 20rpx;
  }

.title a {
  color: #e3e3e5;
  display: flex;
  align-items: center;
}

.title{
  color: #e3e3e5;
  font-size: 36rpx;
  font-weight: 600;
  margin: 44rpx 0 30rpx 0;
}

i.icon:before{
  content: "";
  width: 24rpx;
  height: 24rpx;
  background-size: 100%;
  display: block;
  margin:0 12rpx;
}

.youjiantou:before{
  background: url("static/youjiantou.png") left center no-repeat;
}

.more:before{
  background: url("static/gengduo.png") left center no-repeat;
}

.huanyipi:before{
  background: url("static/huanyipi.png") left center no-repeat;
}
.guankan:before{
  background: url("static/guankan.png") left center no-repeat;
}
.zan:before{
  background: url("static/zan.png") left center no-repeat;
}
.xiajiantou:before{
  background: url("static/xiajiantou.png") left center no-repeat;
}
.fanhui:before{
  background: url("static/fanhui.png") left center no-repeat;
}
.renqi2:before{
  background: url("static/renqi2.png") left center no-repeat;
}
.shanchu:before{
  background: url("static/shanchu.png") left center no-repeat;
}
.qingkong:before{
  background: url("static/qingkong.png") left center no-repeat;
}

.sousuo:before{
  background: url("static/sousuo.png") left center no-repeat;
  width: 42rpx!important;
  height: 42rpx!important;
  margin: 0!important;
}
.lishi:before{
  background: url("static/lishi.png") left center no-repeat;
  width: 42rpx!important;
  height: 42rpx!important;
}
.pindao:before{
  background: url("static/pindao.png") left center no-repeat;
  width: 42rpx!important;
  height: 42rpx!important;
}
.save:before{
  background: url("static/save.png") left center no-repeat;
  width: 42rpx!important;
  height: 42rpx!important;
}



.list {
  display: flex;
  flex-wrap: wrap;
  gap: 0 20rpx;

  li {
    width: calc(50% - 10rpx);

    navigator {
      margin-bottom: 28rpx;
      position: relative;

    }
    .biaoti{
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2; /* 限制显示2行 */
    }
  }

  image {
    width: 100%;
    height: 100%;
  }

  .video-info {
    position: absolute;
    width: calc(100% - 20rpx);
    padding: 0 10rpx;
    bottom: 0;
    height: 48rpx;
    font-size: 20rpx;
    line-height: 48rpx;
    background: linear-gradient(0deg, #000000c2, rgba(16, 16, 18, 0));
    display: flex;
    flex-direction: column;
    justify-content: flex-end;

    .renqitime {
      display: flex;
      align-items: center;
      justify-content: space-between;

      span:first-child{
        display: flex;
        align-items: center;
      }

      span:first-child:before {
        content: "";
        background: url("static/renqi.png") left center no-repeat;
        width: 24rpx;
        height: 24rpx;
        background-size: 100%;
        display: block;
        margin-right: 6rpx;
      }
    }
    .pingfen{
      color: #ff743d;
      font-size: 30rpx;
      font-weight: 600;
      line-height: 30rpx;
    }

  }

  .tags {
    height: 28rpx;
    line-height: 28rpx;
    font-size: 24rpx;
    padding-top: 10rpx;
    color: #6f6f71;
    gap: 16rpx;
    display: flex;
    overflow: hidden;
  }
}

.cover {
  position: relative;
  height: 60vw;
}


.swiper {
  height: 356rpx;

  .swiper-item {
    display: block;
    height: 356rpx;
    line-height: 356rpx;
    text-align: center;
    position: relative;

    image{
      width: 100%;
      display: block;
    }
    .biaoti{
      position: absolute;
      bottom: 0;
      height: 80rpx;
      line-height: 80rpx;
      color: #ffffffc7;
      background: linear-gradient(0deg, #101012, rgba(16, 16, 18, 0));
      width: calc(100% - 40rpx);
      padding: 0 20rpx;
      text-align: left;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

  }
  .uni-swiper-dots-horizontal{
    right: 20rpx;
    left: auto;
    transform: translate(0, 0);
    z-index: 9999;
    bottom: 10rpx;

    .uni-swiper-dot{
      width: 14rpx;
      height: 3rpx;
      border-radius: 4rpx;
      background: #ffffff90;

    }

    .uni-swiper-dot-active{
      background: #fe748c;
    }
  }

}

</style>
